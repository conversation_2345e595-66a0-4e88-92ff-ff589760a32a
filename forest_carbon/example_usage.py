#!/usr/bin/env python3
"""
Example usage of the Forest Carbon Sequestration API

This script demonstrates how to use the conversational API to calculate
forest carbon sequestration through natural language interaction.
"""

import asyncio
import json
from django.contrib.auth.models import User
from forest_carbon.models import TreeSpecies, GeographicRegion
from forest_carbon.agents import OrchestratorAgent
from forest_carbon.services import ConversationService


async def example_conversation():
    """
    Example of a complete conversation flow for carbon calculation
    """
    print("🌲 Forest Carbon Sequestration Example 🌲\n")
    
    # Create a test user (in a real app, this would be the authenticated user)
    user, created = User.objects.get_or_create(
        username='example_user',
        defaults={'email': '<EMAIL>'}
    )
    
    # Initialize the orchestrator agent
    orchestrator = OrchestratorAgent()
    
    # Start a new conversation session
    session = ConversationService.create_session(user=user)
    print(f"Started conversation session: {session.session_id}")
    print(f"Initial step: {session.current_step}\n")
    
    # Simulate a conversation flow
    conversation_steps = [
        {
            'user_message': 'I have oak trees in my forest',
            'description': 'User provides species information'
        },
        {
            'user_message': 'The forest is located in California, USA',
            'description': 'User provides location information'
        },
        {
            'user_message': 'The forest area is 25 hectares',
            'description': 'User provides forest area'
        },
        {
            'user_message': 'The average DBH is 30 cm, height is 18 meters, and we have about 400 trees per hectare',
            'description': 'User provides tree measurements'
        },
        {
            'user_message': 'The forest is 35 years old and naturally managed',
            'description': 'User provides forest characteristics'
        },
        {
            'user_message': 'Yes, that information is correct',
            'description': 'User confirms the collected data'
        }
    ]
    
    print("Starting conversation simulation...\n")
    print("=" * 60)
    
    for i, step in enumerate(conversation_steps, 1):
        print(f"\nStep {i}: {step['description']}")
        print(f"User: {step['user_message']}")
        print("-" * 40)
        
        try:
            # Process the user message
            response = await orchestrator.process_user_message(session, step['user_message'])
            
            # Display agent response
            print(f"Agent ({response['response_type']}): {response['message']}")
            
            # Show progress
            progress = response['completion_status'] * 100
            print(f"Progress: {progress:.1f}%")
            
            # Show collected data
            if response['data_status']:
                print("Collected data:")
                for key, value in response['data_status'].items():
                    print(f"  {key}: {value}")
            
            # Check if calculation is complete
            if response['response_type'] == 'result' and 'calculation_results' in response:
                print("\n🎉 Calculation Complete! 🎉")
                results = response['calculation_results']
                print(f"Total Biomass: {results['total_biomass']:.2f} tons")
                print(f"Total Carbon: {results['total_carbon']:.2f} tons")
                print(f"Annual CO2 Sequestration: {results['annual_sequestration']:.2f} tons/year")
                break
            
            # Refresh session for next iteration
            session.refresh_from_db()
            
        except Exception as e:
            print(f"Error: {str(e)}")
            break
        
        print("=" * 60)
    
    print(f"\nFinal session status: {session.status}")
    print(f"Completion: {session.completion_percentage}%")


def example_direct_calculation():
    """
    Example of direct calculation without conversation
    """
    print("\n🧮 Direct Calculation Example 🧮\n")
    
    from forest_carbon.calculations import CarbonCalculator, ForestParameters
    
    # Define forest parameters
    params = ForestParameters(
        area_hectares=25.0,
        tree_density=400.0,
        average_dbh=30.0,
        average_height=18.0,
        forest_age=35,
        allometric_a=0.0673,
        allometric_b=2.085,
        allometric_c=0.756,
        wood_density=0.65,
        carbon_content=0.47,
        annual_growth_rate=0.015,
        climate_factor=1.0,
        soil_factor=1.0
    )
    
    print("Forest Parameters:")
    print(f"  Area: {params.area_hectares} hectares")
    print(f"  Tree Density: {params.tree_density} trees/hectare")
    print(f"  Average DBH: {params.average_dbh} cm")
    print(f"  Average Height: {params.average_height} m")
    print(f"  Forest Age: {params.forest_age} years")
    print(f"  Wood Density: {params.wood_density} g/cm³")
    print(f"  Carbon Content: {params.carbon_content * 100}%")
    print(f"  Annual Growth Rate: {params.annual_growth_rate * 100}%")
    
    # Perform calculation
    result = CarbonCalculator.calculate_forest_carbon_sequestration(params)
    
    print("\nCalculation Results:")
    print(f"  Total Biomass: {result.total_biomass:.2f} tons")
    print(f"  Above-ground Biomass: {result.above_ground_biomass:.2f} tons")
    print(f"  Below-ground Biomass: {result.below_ground_biomass:.2f} tons")
    print(f"  Total Carbon: {result.total_carbon:.2f} tons")
    print(f"  Annual CO2 Sequestration: {result.annual_sequestration:.2f} tons/year")
    
    # Show calculation details
    print("\nCalculation Details:")
    details = result.calculation_details
    if 'intermediate_calculations' in details:
        intermediate = details['intermediate_calculations']
        print(f"  Total Trees: {intermediate.get('total_trees', 'N/A')}")
        print(f"  Biomass per Tree: {intermediate.get('biomass_per_tree_kg', 'N/A')} kg")
        print(f"  Root-to-Shoot Ratio: {intermediate.get('root_to_shoot_ratio', 'N/A')}")


def example_validation():
    """
    Example of input validation
    """
    print("\n✅ Validation Example ✅\n")
    
    from forest_carbon.validators import ForestDataValidator
    
    # Test valid data
    print("Testing valid forest data:")
    valid_data = {
        'species_name': 'Oak',
        'location': 'California, USA',
        'area': '25.5',
        'area_unit': 'hectares',
        'dbh': '30.0',
        'height': '18.0',
        'density': '400',
        'age': '35',
        'management_type': 'natural'
    }
    
    is_valid, errors, validated = ForestDataValidator.validate_complete_forest_data(valid_data)
    
    if is_valid:
        print("✅ All data is valid!")
        print("Validated data:")
        for key, value in validated.items():
            print(f"  {key}: {value}")
    else:
        print("❌ Validation errors found:")
        for error in errors:
            print(f"  {error.field}: {error.message}")
    
    # Test invalid data
    print("\nTesting invalid forest data:")
    invalid_data = valid_data.copy()
    invalid_data['area'] = '-5'  # Invalid negative area
    invalid_data['dbh'] = 'abc'  # Invalid non-numeric DBH
    
    is_valid, errors, validated = ForestDataValidator.validate_complete_forest_data(invalid_data)
    
    if not is_valid:
        print("❌ Validation errors found (as expected):")
        for error in errors:
            print(f"  {error.field}: {error.message}")
            if error.suggestions:
                print(f"    Suggestions: {', '.join(error.suggestions)}")


def setup_test_data():
    """
    Set up test species and regions for the examples
    """
    # Create test species
    oak, created = TreeSpecies.objects.get_or_create(
        name='Oak',
        defaults={
            'scientific_name': 'Quercus spp.',
            'allometric_a': 0.0673,
            'allometric_b': 2.085,
            'allometric_c': 0.756,
            'wood_density': 0.65,
            'carbon_content': 0.47,
            'annual_growth_rate': 0.015,
        }
    )
    
    # Create test region
    california, created = GeographicRegion.objects.get_or_create(
        name='California',
        defaults={
            'country': 'United States',
            'latitude': 36.0,
            'longitude': -119.0,
            'average_temperature': 16.0,
            'annual_precipitation': 600.0,
            'growing_season_length': 200,
            'climate_factor': 1.0,
            'soil_factor': 1.0,
        }
    )
    
    print("Test data setup complete!")
    return oak, california


async def main():
    """
    Main function to run all examples
    """
    print("Forest Carbon Sequestration API Examples")
    print("=" * 50)
    
    # Set up test data
    setup_test_data()
    
    # Run examples
    example_validation()
    example_direct_calculation()
    
    # Note: Conversation example requires running Django with proper setup
    print("\n📝 Note: To run the conversation example, use Django management command:")
    print("python manage.py shell -c 'from forest_carbon.example_usage import example_conversation; import asyncio; asyncio.run(example_conversation())'")


if __name__ == "__main__":
    # This would be run as a Django management command or in Django shell
    print("Run this script in Django environment:")
    print("python manage.py shell -c 'from forest_carbon.example_usage import main; import asyncio; asyncio.run(main())'")
