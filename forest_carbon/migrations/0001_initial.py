# Generated by Django 5.2.5 on 2025-08-14 02:59

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="GeographicRegion",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        max_length=200, unique=True, verbose_name="Region Name"
                    ),
                ),
                ("country", models.CharField(max_length=100, verbose_name="Country")),
                ("latitude", models.FloatField(verbose_name="Latitude")),
                ("longitude", models.FloatField(verbose_name="Longitude")),
                (
                    "average_temperature",
                    models.FloatField(verbose_name="Average Temperature (°C)"),
                ),
                (
                    "annual_precipitation",
                    models.FloatField(verbose_name="Annual Precipitation (mm)"),
                ),
                (
                    "growing_season_length",
                    models.IntegerField(verbose_name="Growing Season Length (days)"),
                ),
                (
                    "climate_factor",
                    models.FloatField(
                        default=1.0, verbose_name="Climate Adjustment Factor"
                    ),
                ),
                (
                    "soil_factor",
                    models.FloatField(default=1.0, verbose_name="Soil Quality Factor"),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Geographic Region",
                "verbose_name_plural": "Geographic Regions",
            },
        ),
        migrations.CreateModel(
            name="TreeSpecies",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        max_length=200, unique=True, verbose_name="Species Name"
                    ),
                ),
                (
                    "scientific_name",
                    models.CharField(
                        blank=True, max_length=200, verbose_name="Scientific Name"
                    ),
                ),
                (
                    "allometric_a",
                    models.FloatField(
                        default=0.0673, verbose_name="Allometric Parameter A"
                    ),
                ),
                (
                    "allometric_b",
                    models.FloatField(
                        default=2.085, verbose_name="Allometric Parameter B"
                    ),
                ),
                (
                    "allometric_c",
                    models.FloatField(
                        default=0.756, verbose_name="Allometric Parameter C"
                    ),
                ),
                (
                    "allometric_d",
                    models.FloatField(
                        default=0.0, verbose_name="Allometric Parameter D"
                    ),
                ),
                (
                    "wood_density",
                    models.FloatField(default=0.5, verbose_name="Wood Density (g/cm³)"),
                ),
                (
                    "carbon_content",
                    models.FloatField(default=0.47, verbose_name="Carbon Content (%)"),
                ),
                (
                    "annual_growth_rate",
                    models.FloatField(default=0.02, verbose_name="Annual Growth Rate"),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Tree Species",
                "verbose_name_plural": "Tree Species",
            },
        ),
        migrations.CreateModel(
            name="ConversationSession",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "session_id",
                    models.UUIDField(
                        default=uuid.uuid4, unique=True, verbose_name="Session ID"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("completed", "Completed"),
                            ("abandoned", "Abandoned"),
                        ],
                        default="active",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "collected_data",
                    models.JSONField(default=dict, verbose_name="Collected Data"),
                ),
                (
                    "current_step",
                    models.CharField(
                        default="species", max_length=50, verbose_name="Current Step"
                    ),
                ),
                (
                    "completion_percentage",
                    models.FloatField(default=0.0, verbose_name="Completion %"),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="User",
                    ),
                ),
            ],
            options={
                "verbose_name": "Conversation Session",
                "verbose_name_plural": "Conversation Sessions",
            },
        ),
        migrations.CreateModel(
            name="AgentInteraction",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "interaction_id",
                    models.UUIDField(
                        default=uuid.uuid4, unique=True, verbose_name="Interaction ID"
                    ),
                ),
                (
                    "sender_agent",
                    models.CharField(
                        choices=[
                            ("orchestrator", "Orchestrator Agent"),
                            ("input_extraction", "Input Extraction Agent"),
                            ("calculation", "Calculation Agent"),
                            ("search_engine", "Search Engine Agent"),
                            ("validation", "Validation Agent"),
                        ],
                        max_length=50,
                        verbose_name="Sender Agent",
                    ),
                ),
                (
                    "receiver_agent",
                    models.CharField(
                        choices=[
                            ("orchestrator", "Orchestrator Agent"),
                            ("input_extraction", "Input Extraction Agent"),
                            ("calculation", "Calculation Agent"),
                            ("search_engine", "Search Engine Agent"),
                            ("validation", "Validation Agent"),
                        ],
                        max_length=50,
                        verbose_name="Receiver Agent",
                    ),
                ),
                (
                    "message_type",
                    models.CharField(
                        choices=[
                            ("request", "Request"),
                            ("response", "Response"),
                            ("error", "Error"),
                            ("info", "Information"),
                        ],
                        max_length=20,
                        verbose_name="Message Type",
                    ),
                ),
                ("message_content", models.JSONField(verbose_name="Message Content")),
                (
                    "execution_time",
                    models.FloatField(
                        blank=True, null=True, verbose_name="Execution Time (seconds)"
                    ),
                ),
                ("success", models.BooleanField(default=True, verbose_name="Success")),
                (
                    "error_details",
                    models.TextField(blank=True, verbose_name="Error Details"),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "session",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="forest_carbon.conversationsession",
                        verbose_name="Session",
                    ),
                ),
            ],
            options={
                "verbose_name": "Agent Interaction",
                "verbose_name_plural": "Agent Interactions",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="ForestData",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("area", models.FloatField(verbose_name="Forest Area")),
                (
                    "area_unit",
                    models.CharField(
                        choices=[("hectares", "Hectares"), ("acres", "Acres")],
                        default="hectares",
                        max_length=20,
                        verbose_name="Area Unit",
                    ),
                ),
                ("average_dbh", models.FloatField(verbose_name="Average DBH (cm)")),
                (
                    "average_height",
                    models.FloatField(verbose_name="Average Height (m)"),
                ),
                ("tree_density", models.FloatField(verbose_name="Trees per Hectare")),
                ("forest_age", models.IntegerField(verbose_name="Forest Age (years)")),
                (
                    "management_type",
                    models.CharField(
                        default="natural",
                        max_length=100,
                        verbose_name="Management Type",
                    ),
                ),
                (
                    "additional_parameters",
                    models.JSONField(
                        default=dict, verbose_name="Additional Parameters"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "session",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="forest_carbon.conversationsession",
                        verbose_name="Session",
                    ),
                ),
                (
                    "region",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="forest_carbon.geographicregion",
                        verbose_name="Geographic Region",
                    ),
                ),
                (
                    "species",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="forest_carbon.treespecies",
                        verbose_name="Tree Species",
                    ),
                ),
            ],
            options={
                "verbose_name": "Forest Data",
                "verbose_name_plural": "Forest Data",
            },
        ),
        migrations.CreateModel(
            name="CarbonCalculationResult",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "calculation_id",
                    models.UUIDField(
                        default=uuid.uuid4, unique=True, verbose_name="Calculation ID"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("calculating", "Calculating"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                        ],
                        default="pending",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "total_biomass",
                    models.FloatField(
                        blank=True, null=True, verbose_name="Total Biomass (tons)"
                    ),
                ),
                (
                    "total_carbon",
                    models.FloatField(
                        blank=True, null=True, verbose_name="Total Carbon (tons)"
                    ),
                ),
                (
                    "annual_sequestration",
                    models.FloatField(
                        blank=True,
                        null=True,
                        verbose_name="Annual Sequestration (tons CO2/year)",
                    ),
                ),
                (
                    "above_ground_biomass",
                    models.FloatField(
                        blank=True,
                        null=True,
                        verbose_name="Above Ground Biomass (tons)",
                    ),
                ),
                (
                    "below_ground_biomass",
                    models.FloatField(
                        blank=True,
                        null=True,
                        verbose_name="Below Ground Biomass (tons)",
                    ),
                ),
                (
                    "calculation_method",
                    models.CharField(
                        default="allometric",
                        max_length=100,
                        verbose_name="Calculation Method",
                    ),
                ),
                (
                    "parameters_used",
                    models.JSONField(default=dict, verbose_name="Parameters Used"),
                ),
                (
                    "calculation_details",
                    models.JSONField(default=dict, verbose_name="Calculation Details"),
                ),
                (
                    "error_message",
                    models.TextField(blank=True, verbose_name="Error Message"),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "session",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="forest_carbon.conversationsession",
                        verbose_name="Session",
                    ),
                ),
                (
                    "forest_data",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="forest_carbon.forestdata",
                        verbose_name="Forest Data",
                    ),
                ),
            ],
            options={
                "verbose_name": "Carbon Calculation Result",
                "verbose_name_plural": "Carbon Calculation Results",
            },
        ),
        migrations.CreateModel(
            name="UserMessage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "message_type",
                    models.CharField(
                        choices=[
                            ("user", "User Message"),
                            ("system", "System Response"),
                            ("agent", "Agent Message"),
                        ],
                        max_length=20,
                        verbose_name="Message Type",
                    ),
                ),
                ("content", models.TextField(verbose_name="Message Content")),
                (
                    "agent_name",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="Agent Name"
                    ),
                ),
                (
                    "extracted_data",
                    models.JSONField(default=dict, verbose_name="Extracted Data"),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "session",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="forest_carbon.conversationsession",
                        verbose_name="Session",
                    ),
                ),
            ],
            options={
                "verbose_name": "User Message",
                "verbose_name_plural": "User Messages",
                "ordering": ["created_at"],
            },
        ),
    ]
