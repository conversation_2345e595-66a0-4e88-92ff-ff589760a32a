from django.db import models
from django.contrib.auth.models import User
import uuid
import json


class TreeSpecies(models.Model):
    """
    Model to store tree species information and their carbon calculation parameters
    """
    name = models.CharField(max_length=200, unique=True, verbose_name="Species Name")
    scientific_name = models.CharField(max_length=200, blank=True, verbose_name="Scientific Name")

    # Allometric equation parameters for biomass calculation
    # Biomass = a * (DBH^b) * (Height^c) * Wood_density^d
    allometric_a = models.FloatField(default=0.0673, verbose_name="Allometric Parameter A")
    allometric_b = models.FloatField(default=2.085, verbose_name="Allometric Parameter B")
    allometric_c = models.FloatField(default=0.756, verbose_name="Allometric Parameter C")
    allometric_d = models.FloatField(default=0.0, verbose_name="Allometric Parameter D")

    # Wood density (g/cm³)
    wood_density = models.FloatField(default=0.5, verbose_name="Wood Density (g/cm³)")

    # Carbon content percentage (typically 45-50% for most species)
    carbon_content = models.FloatField(default=0.47, verbose_name="Carbon Content (%)")

    # Growth rate parameters
    annual_growth_rate = models.FloatField(default=0.02, verbose_name="Annual Growth Rate")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Tree Species"
        verbose_name_plural = "Tree Species"

    def __str__(self):
        return self.name


class GeographicRegion(models.Model):
    """
    Model to store geographic region information and climate data
    """
    name = models.CharField(max_length=200, unique=True, verbose_name="Region Name")
    country = models.CharField(max_length=100, verbose_name="Country")
    latitude = models.FloatField(verbose_name="Latitude")
    longitude = models.FloatField(verbose_name="Longitude")

    # Climate factors affecting carbon sequestration
    average_temperature = models.FloatField(verbose_name="Average Temperature (°C)")
    annual_precipitation = models.FloatField(verbose_name="Annual Precipitation (mm)")
    growing_season_length = models.IntegerField(verbose_name="Growing Season Length (days)")

    # Regional adjustment factors
    climate_factor = models.FloatField(default=1.0, verbose_name="Climate Adjustment Factor")
    soil_factor = models.FloatField(default=1.0, verbose_name="Soil Quality Factor")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Geographic Region"
        verbose_name_plural = "Geographic Regions"

    def __str__(self):
        return f"{self.name}, {self.country}"


class ConversationSession(models.Model):
    """
    Model to track user conversation sessions for data collection
    """
    SESSION_STATUS_CHOICES = [
        ('active', 'Active'),
        ('completed', 'Completed'),
        ('abandoned', 'Abandoned'),
    ]

    session_id = models.UUIDField(default=uuid.uuid4, unique=True, verbose_name="Session ID")
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True, verbose_name="User")
    status = models.CharField(max_length=20, choices=SESSION_STATUS_CHOICES, default='active', verbose_name="Status")

    # Collected data during conversation
    collected_data = models.JSONField(default=dict, verbose_name="Collected Data")

    # Progress tracking
    current_step = models.CharField(max_length=50, default='species', verbose_name="Current Step")
    completion_percentage = models.FloatField(default=0.0, verbose_name="Completion %")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Conversation Session"
        verbose_name_plural = "Conversation Sessions"

    def __str__(self):
        return f"Session {self.session_id} - {self.status}"


class ForestData(models.Model):
    """
    Model to store forest data collected from users
    """
    AREA_UNIT_CHOICES = [
        ('hectares', 'Hectares'),
        ('acres', 'Acres'),
    ]

    session = models.ForeignKey(ConversationSession, on_delete=models.CASCADE, verbose_name="Session")
    species = models.ForeignKey(TreeSpecies, on_delete=models.CASCADE, verbose_name="Tree Species")
    region = models.ForeignKey(GeographicRegion, on_delete=models.CASCADE, verbose_name="Geographic Region")

    # Forest area
    area = models.FloatField(verbose_name="Forest Area")
    area_unit = models.CharField(max_length=20, choices=AREA_UNIT_CHOICES, default='hectares', verbose_name="Area Unit")

    # Tree measurements
    average_dbh = models.FloatField(verbose_name="Average DBH (cm)")
    average_height = models.FloatField(verbose_name="Average Height (m)")
    tree_density = models.FloatField(verbose_name="Trees per Hectare")

    # Forest characteristics
    forest_age = models.IntegerField(verbose_name="Forest Age (years)")
    management_type = models.CharField(max_length=100, default='natural', verbose_name="Management Type")

    # Additional parameters
    additional_parameters = models.JSONField(default=dict, verbose_name="Additional Parameters")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Forest Data"
        verbose_name_plural = "Forest Data"

    def __str__(self):
        return f"Forest Data - {self.species.name} in {self.region.name}"

    def get_area_in_hectares(self):
        """Convert area to hectares if needed"""
        if self.area_unit == 'acres':
            return self.area * 0.404686  # Convert acres to hectares
        return self.area


class CarbonCalculationResult(models.Model):
    """
    Model to store carbon sequestration calculation results
    """
    CALCULATION_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('calculating', 'Calculating'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ]

    calculation_id = models.UUIDField(default=uuid.uuid4, unique=True, verbose_name="Calculation ID")
    forest_data = models.ForeignKey(ForestData, on_delete=models.CASCADE, verbose_name="Forest Data")
    session = models.ForeignKey(ConversationSession, on_delete=models.CASCADE, verbose_name="Session")

    status = models.CharField(max_length=20, choices=CALCULATION_STATUS_CHOICES, default='pending', verbose_name="Status")

    # Calculation results
    total_biomass = models.FloatField(null=True, blank=True, verbose_name="Total Biomass (tons)")
    total_carbon = models.FloatField(null=True, blank=True, verbose_name="Total Carbon (tons)")
    annual_sequestration = models.FloatField(null=True, blank=True, verbose_name="Annual Sequestration (tons CO2/year)")

    # Detailed breakdown
    above_ground_biomass = models.FloatField(null=True, blank=True, verbose_name="Above Ground Biomass (tons)")
    below_ground_biomass = models.FloatField(null=True, blank=True, verbose_name="Below Ground Biomass (tons)")

    # Calculation metadata
    calculation_method = models.CharField(max_length=100, default='allometric', verbose_name="Calculation Method")
    parameters_used = models.JSONField(default=dict, verbose_name="Parameters Used")
    calculation_details = models.JSONField(default=dict, verbose_name="Calculation Details")

    # Error handling
    error_message = models.TextField(blank=True, verbose_name="Error Message")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Carbon Calculation Result"
        verbose_name_plural = "Carbon Calculation Results"

    def __str__(self):
        return f"Calculation {self.calculation_id} - {self.status}"


class AgentInteraction(models.Model):
    """
    Model to track interactions between different agents in the multi-agent system
    """
    AGENT_TYPE_CHOICES = [
        ('orchestrator', 'Orchestrator Agent'),
        ('input_extraction', 'Input Extraction Agent'),
        ('calculation', 'Calculation Agent'),
        ('search_engine', 'Search Engine Agent'),
        ('validation', 'Validation Agent'),
    ]

    MESSAGE_TYPE_CHOICES = [
        ('request', 'Request'),
        ('response', 'Response'),
        ('error', 'Error'),
        ('info', 'Information'),
    ]

    interaction_id = models.UUIDField(default=uuid.uuid4, unique=True, verbose_name="Interaction ID")
    session = models.ForeignKey(ConversationSession, on_delete=models.CASCADE, verbose_name="Session")

    # Agent information
    sender_agent = models.CharField(max_length=50, choices=AGENT_TYPE_CHOICES, verbose_name="Sender Agent")
    receiver_agent = models.CharField(max_length=50, choices=AGENT_TYPE_CHOICES, verbose_name="Receiver Agent")

    # Message details
    message_type = models.CharField(max_length=20, choices=MESSAGE_TYPE_CHOICES, verbose_name="Message Type")
    message_content = models.JSONField(verbose_name="Message Content")

    # Execution details
    execution_time = models.FloatField(null=True, blank=True, verbose_name="Execution Time (seconds)")
    success = models.BooleanField(default=True, verbose_name="Success")
    error_details = models.TextField(blank=True, verbose_name="Error Details")

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Agent Interaction"
        verbose_name_plural = "Agent Interactions"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.sender_agent} -> {self.receiver_agent}: {self.message_type}"


class UserMessage(models.Model):
    """
    Model to store user messages and system responses during conversation
    """
    MESSAGE_TYPE_CHOICES = [
        ('user', 'User Message'),
        ('system', 'System Response'),
        ('agent', 'Agent Message'),
    ]

    session = models.ForeignKey(ConversationSession, on_delete=models.CASCADE, verbose_name="Session")
    message_type = models.CharField(max_length=20, choices=MESSAGE_TYPE_CHOICES, verbose_name="Message Type")
    content = models.TextField(verbose_name="Message Content")

    # Metadata
    agent_name = models.CharField(max_length=100, blank=True, verbose_name="Agent Name")
    extracted_data = models.JSONField(default=dict, verbose_name="Extracted Data")

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "User Message"
        verbose_name_plural = "User Messages"
        ordering = ['created_at']

    def __str__(self):
        return f"{self.message_type}: {self.content[:50]}..."
