"""
Forest Carbon Sequestration Services

This module provides service classes that integrate the calculation engine
with Django models and handle business logic for the carbon sequestration system.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Tuple
from django.db import transaction
from django.utils import timezone

from .models import (
    TreeSpecies, GeographicRegion, ConversationSession, 
    ForestData, CarbonCalculationResult, AgentInteraction, UserMessage
)
from .calculations import CarbonCalculator, ForestParameters, validate_forest_parameters

logger = logging.getLogger(__name__)


class CarbonCalculationService:
    """
    Service class for handling carbon sequestration calculations
    """
    
    @staticmethod
    def create_forest_parameters_from_data(forest_data: ForestData) -> ForestParameters:
        """
        Create ForestParameters object from ForestData model instance.
        
        Args:
            forest_data: ForestData model instance
            
        Returns:
            ForestParameters object ready for calculation
        """
        return ForestParameters(
            area_hectares=forest_data.get_area_in_hectares(),
            tree_density=forest_data.tree_density,
            average_dbh=forest_data.average_dbh,
            average_height=forest_data.average_height,
            forest_age=forest_data.forest_age,
            allometric_a=forest_data.species.allometric_a,
            allometric_b=forest_data.species.allometric_b,
            allometric_c=forest_data.species.allometric_c,
            allometric_d=forest_data.species.allometric_d,
            wood_density=forest_data.species.wood_density,
            carbon_content=forest_data.species.carbon_content,
            annual_growth_rate=forest_data.species.annual_growth_rate,
            climate_factor=forest_data.region.climate_factor,
            soil_factor=forest_data.region.soil_factor
        )
    
    @staticmethod
    async def calculate_carbon_sequestration(forest_data: ForestData) -> CarbonCalculationResult:
        """
        Perform carbon sequestration calculation for given forest data.
        
        Args:
            forest_data: ForestData model instance
            
        Returns:
            CarbonCalculationResult model instance
        """
        try:
            # Create calculation record
            calc_result = CarbonCalculationResult.objects.create(
                forest_data=forest_data,
                session=forest_data.session,
                status='calculating'
            )
            
            # Create forest parameters
            params = CarbonCalculationService.create_forest_parameters_from_data(forest_data)
            
            # Validate parameters
            is_valid, error_message = validate_forest_parameters(params)
            if not is_valid:
                calc_result.status = 'failed'
                calc_result.error_message = error_message
                calc_result.save()
                return calc_result
            
            # Perform calculation
            result = CarbonCalculator.calculate_forest_carbon_sequestration(params)
            
            # Update calculation record with results
            calc_result.status = 'completed'
            calc_result.total_biomass = result.total_biomass
            calc_result.above_ground_biomass = result.above_ground_biomass
            calc_result.below_ground_biomass = result.below_ground_biomass
            calc_result.total_carbon = result.total_carbon
            calc_result.annual_sequestration = result.annual_sequestration
            calc_result.parameters_used = result.calculation_details['parameters_used']
            calc_result.calculation_details = result.calculation_details
            calc_result.save()
            
            logger.info(f"Carbon calculation completed for forest data {forest_data.id}")
            return calc_result
            
        except Exception as e:
            logger.error(f"Error in carbon calculation: {str(e)}")
            if 'calc_result' in locals():
                calc_result.status = 'failed'
                calc_result.error_message = str(e)
                calc_result.save()
                return calc_result
            raise


class ConversationService:
    """
    Service class for managing conversation sessions and data collection
    """
    
    CONVERSATION_STEPS = [
        'species',
        'location', 
        'area',
        'tree_measurements',
        'forest_characteristics',
        'confirmation',
        'calculation'
    ]
    
    @staticmethod
    def create_session(user=None) -> ConversationSession:
        """
        Create a new conversation session.
        
        Args:
            user: User instance (optional)
            
        Returns:
            ConversationSession instance
        """
        session = ConversationSession.objects.create(
            user=user,
            status='active',
            current_step='species',
            completion_percentage=0.0
        )
        logger.info(f"Created new conversation session {session.session_id}")
        return session
    
    @staticmethod
    def update_session_data(session: ConversationSession, step: str, data: Dict) -> ConversationSession:
        """
        Update session with collected data from a conversation step.
        
        Args:
            session: ConversationSession instance
            step: Current conversation step
            data: Data collected in this step
            
        Returns:
            Updated ConversationSession instance
        """
        # Update collected data
        session.collected_data.update({step: data})
        
        # Update current step and completion percentage
        if step in ConversationService.CONVERSATION_STEPS:
            current_index = ConversationService.CONVERSATION_STEPS.index(step)
            if current_index < len(ConversationService.CONVERSATION_STEPS) - 1:
                session.current_step = ConversationService.CONVERSATION_STEPS[current_index + 1]
            session.completion_percentage = ((current_index + 1) / len(ConversationService.CONVERSATION_STEPS)) * 100
        
        session.save()
        logger.info(f"Updated session {session.session_id} with {step} data")
        return session
    
    @staticmethod
    def get_session_progress(session: ConversationSession) -> Dict:
        """
        Get current progress information for a session.
        
        Args:
            session: ConversationSession instance
            
        Returns:
            Dictionary with progress information
        """
        return {
            'session_id': str(session.session_id),
            'current_step': session.current_step,
            'completion_percentage': session.completion_percentage,
            'collected_data': session.collected_data,
            'status': session.status,
            'steps_remaining': ConversationService.CONVERSATION_STEPS[
                ConversationService.CONVERSATION_STEPS.index(session.current_step):
            ] if session.current_step in ConversationService.CONVERSATION_STEPS else []
        }
    
    @staticmethod
    def complete_session(session: ConversationSession) -> ConversationSession:
        """
        Mark session as completed.
        
        Args:
            session: ConversationSession instance
            
        Returns:
            Updated ConversationSession instance
        """
        session.status = 'completed'
        session.completion_percentage = 100.0
        session.save()
        logger.info(f"Completed conversation session {session.session_id}")
        return session


class AgentInteractionService:
    """
    Service class for tracking agent interactions in the multi-agent system
    """
    
    @staticmethod
    def log_interaction(
        session: ConversationSession,
        sender_agent: str,
        receiver_agent: str,
        message_type: str,
        message_content: Dict,
        execution_time: Optional[float] = None,
        success: bool = True,
        error_details: str = ""
    ) -> AgentInteraction:
        """
        Log an interaction between agents.
        
        Args:
            session: ConversationSession instance
            sender_agent: Name of the sending agent
            receiver_agent: Name of the receiving agent
            message_type: Type of message (request, response, error, info)
            message_content: Content of the message
            execution_time: Time taken for execution (optional)
            success: Whether the interaction was successful
            error_details: Error details if unsuccessful
            
        Returns:
            AgentInteraction instance
        """
        interaction = AgentInteraction.objects.create(
            session=session,
            sender_agent=sender_agent,
            receiver_agent=receiver_agent,
            message_type=message_type,
            message_content=message_content,
            execution_time=execution_time,
            success=success,
            error_details=error_details
        )
        
        logger.info(f"Logged agent interaction: {sender_agent} -> {receiver_agent}")
        return interaction
    
    @staticmethod
    def get_session_interactions(session: ConversationSession) -> List[AgentInteraction]:
        """
        Get all interactions for a session.
        
        Args:
            session: ConversationSession instance
            
        Returns:
            List of AgentInteraction instances
        """
        return AgentInteraction.objects.filter(session=session).order_by('created_at')


class DataValidationService:
    """
    Service class for validating user input data
    """
    
    @staticmethod
    def validate_species_data(species_name: str) -> Tuple[bool, Optional[str], Optional[TreeSpecies]]:
        """
        Validate and retrieve species data.
        
        Args:
            species_name: Name of the tree species
            
        Returns:
            Tuple of (is_valid, error_message, species_object)
        """
        try:
            species = TreeSpecies.objects.get(name__icontains=species_name)
            return True, None, species
        except TreeSpecies.DoesNotExist:
            return False, f"Species '{species_name}' not found in database", None
        except TreeSpecies.MultipleObjectsReturned:
            return False, f"Multiple species found for '{species_name}'. Please be more specific.", None
    
    @staticmethod
    def validate_location_data(location_name: str) -> Tuple[bool, Optional[str], Optional[GeographicRegion]]:
        """
        Validate and retrieve location data.
        
        Args:
            location_name: Name of the geographic region
            
        Returns:
            Tuple of (is_valid, error_message, region_object)
        """
        try:
            region = GeographicRegion.objects.get(name__icontains=location_name)
            return True, None, region
        except GeographicRegion.DoesNotExist:
            return False, f"Location '{location_name}' not found in database", None
        except GeographicRegion.MultipleObjectsReturned:
            return False, f"Multiple locations found for '{location_name}'. Please be more specific.", None
    
    @staticmethod
    def validate_numeric_data(value: str, field_name: str, min_value: float = 0, max_value: float = None) -> Tuple[bool, Optional[str], Optional[float]]:
        """
        Validate numeric input data.
        
        Args:
            value: String value to validate
            field_name: Name of the field for error messages
            min_value: Minimum allowed value
            max_value: Maximum allowed value (optional)
            
        Returns:
            Tuple of (is_valid, error_message, numeric_value)
        """
        try:
            numeric_value = float(value)
            
            if numeric_value < min_value:
                return False, f"{field_name} must be at least {min_value}", None
            
            if max_value is not None and numeric_value > max_value:
                return False, f"{field_name} must not exceed {max_value}", None
            
            return True, None, numeric_value
            
        except ValueError:
            return False, f"{field_name} must be a valid number", None
