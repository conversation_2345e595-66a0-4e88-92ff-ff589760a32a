"""
Search Service for Forest Carbon Sequestration

This module provides search functionality to retrieve species-specific parameters,
regional climate data, and other required coefficients from various sources.
"""

import asyncio
import aiohttp
import logging
from typing import Dict, List, Optional, Tuple
from django.conf import settings
from django.db.models import Q

from .models import TreeSpecies, GeographicRegion

logger = logging.getLogger(__name__)


class ForestDataSearchService:
    """
    Service for searching forest-related data from multiple sources
    """
    
    def __init__(self):
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def search_species_fuzzy(self, species_name: str, limit: int = 5) -> List[TreeSpecies]:
        """
        Perform fuzzy search for tree species in the database
        
        Args:
            species_name: Name or partial name of the species
            limit: Maximum number of results to return
            
        Returns:
            List of matching TreeSpecies objects
        """
        try:
            # Clean and prepare search term
            search_term = species_name.strip().lower()
            
            # Perform fuzzy search using Q objects
            species_queryset = TreeSpecies.objects.filter(
                Q(name__icontains=search_term) |
                Q(scientific_name__icontains=search_term)
            ).order_by('name')[:limit]
            
            return list(species_queryset)
            
        except Exception as e:
            logger.error(f"Error in species fuzzy search: {str(e)}")
            return []
    
    async def search_regions_fuzzy(self, location_name: str, limit: int = 5) -> List[GeographicRegion]:
        """
        Perform fuzzy search for geographic regions in the database
        
        Args:
            location_name: Name or partial name of the location
            limit: Maximum number of results to return
            
        Returns:
            List of matching GeographicRegion objects
        """
        try:
            # Clean and prepare search term
            search_term = location_name.strip().lower()
            
            # Perform fuzzy search using Q objects
            regions_queryset = GeographicRegion.objects.filter(
                Q(name__icontains=search_term) |
                Q(country__icontains=search_term)
            ).order_by('name')[:limit]
            
            return list(regions_queryset)
            
        except Exception as e:
            logger.error(f"Error in regions fuzzy search: {str(e)}")
            return []
    
    async def search_species_by_coordinates(
        self, 
        latitude: float, 
        longitude: float, 
        radius_km: float = 100
    ) -> List[Dict]:
        """
        Search for common tree species in a geographic area
        
        Args:
            latitude: Latitude coordinate
            longitude: Longitude coordinate
            radius_km: Search radius in kilometers
            
        Returns:
            List of species information dictionaries
        """
        try:
            # Find nearby regions
            nearby_regions = await self._find_nearby_regions(latitude, longitude, radius_km)
            
            if not nearby_regions:
                return []
            
            # For now, return common species for the climate type
            # In a real implementation, this would query external databases
            climate_type = self._determine_climate_type(nearby_regions[0])
            common_species = self._get_common_species_for_climate(climate_type)
            
            return common_species
            
        except Exception as e:
            logger.error(f"Error searching species by coordinates: {str(e)}")
            return []
    
    async def search_climate_data(
        self, 
        latitude: float, 
        longitude: float
    ) -> Optional[Dict]:
        """
        Search for climate data for given coordinates
        
        Args:
            latitude: Latitude coordinate
            longitude: Longitude coordinate
            
        Returns:
            Dictionary with climate data or None if not found
        """
        try:
            # Try to find existing region data first
            nearby_regions = await self._find_nearby_regions(latitude, longitude, 50)
            
            if nearby_regions:
                region = nearby_regions[0]
                return {
                    'source': 'database',
                    'average_temperature': region.average_temperature,
                    'annual_precipitation': region.annual_precipitation,
                    'growing_season_length': region.growing_season_length,
                    'climate_factor': region.climate_factor,
                    'soil_factor': region.soil_factor,
                    'confidence': 0.8
                }
            
            # If no nearby regions, try external API (placeholder)
            climate_data = await self._fetch_external_climate_data(latitude, longitude)
            return climate_data
            
        except Exception as e:
            logger.error(f"Error searching climate data: {str(e)}")
            return None
    
    async def search_allometric_parameters(
        self, 
        species_name: str, 
        region_name: Optional[str] = None
    ) -> Optional[Dict]:
        """
        Search for allometric parameters for a species
        
        Args:
            species_name: Name of the tree species
            region_name: Optional region name for regional adjustments
            
        Returns:
            Dictionary with allometric parameters or None if not found
        """
        try:
            # First try exact match
            species_matches = await self.search_species_fuzzy(species_name, 1)
            
            if species_matches:
                species = species_matches[0]
                return {
                    'source': 'database',
                    'species_id': species.id,
                    'allometric_a': species.allometric_a,
                    'allometric_b': species.allometric_b,
                    'allometric_c': species.allometric_c,
                    'allometric_d': species.allometric_d,
                    'wood_density': species.wood_density,
                    'carbon_content': species.carbon_content,
                    'annual_growth_rate': species.annual_growth_rate,
                    'confidence': 0.9
                }
            
            # If no exact match, try to find similar species
            similar_params = await self._find_similar_species_parameters(species_name)
            return similar_params
            
        except Exception as e:
            logger.error(f"Error searching allometric parameters: {str(e)}")
            return None
    
    async def _find_nearby_regions(
        self, 
        latitude: float, 
        longitude: float, 
        radius_km: float
    ) -> List[GeographicRegion]:
        """Find regions within a certain radius of given coordinates"""
        try:
            # Simple distance calculation (not precise for large distances)
            lat_range = radius_km / 111.0  # Approximate km per degree latitude
            lon_range = radius_km / (111.0 * abs(latitude) / 90.0 + 1)  # Rough longitude adjustment
            
            regions = GeographicRegion.objects.filter(
                latitude__range=(latitude - lat_range, latitude + lat_range),
                longitude__range=(longitude - lon_range, longitude + lon_range)
            )
            
            return list(regions)
            
        except Exception as e:
            logger.error(f"Error finding nearby regions: {str(e)}")
            return []
    
    def _determine_climate_type(self, region: GeographicRegion) -> str:
        """Determine climate type based on region data"""
        temp = region.average_temperature
        precip = region.annual_precipitation
        
        if temp < 0:
            return 'arctic'
        elif temp < 10:
            return 'boreal'
        elif temp < 20:
            if precip > 1000:
                return 'temperate_humid'
            else:
                return 'temperate_dry'
        else:
            if precip > 1500:
                return 'tropical_humid'
            else:
                return 'tropical_dry'
    
    def _get_common_species_for_climate(self, climate_type: str) -> List[Dict]:
        """Get common species for a climate type"""
        climate_species_map = {
            'arctic': ['Spruce', 'Fir'],
            'boreal': ['Spruce', 'Fir', 'Pine', 'Birch'],
            'temperate_humid': ['Oak', 'Maple', 'Beech', 'Pine'],
            'temperate_dry': ['Pine', 'Oak', 'Cedar'],
            'tropical_humid': ['Eucalyptus', 'Various tropical hardwoods'],
            'tropical_dry': ['Eucalyptus', 'Acacia']
        }
        
        species_names = climate_species_map.get(climate_type, ['Oak', 'Pine'])
        
        return [
            {
                'name': name,
                'confidence': 0.6,
                'source': 'climate_inference'
            }
            for name in species_names
        ]
    
    async def _fetch_external_climate_data(
        self, 
        latitude: float, 
        longitude: float
    ) -> Optional[Dict]:
        """
        Fetch climate data from external APIs (placeholder implementation)
        
        In a real implementation, this would call APIs like:
        - OpenWeatherMap
        - WorldClim
        - NASA Climate Data
        """
        try:
            # Placeholder implementation - return estimated values based on latitude
            estimated_temp = 25 - abs(latitude) * 0.5  # Rough temperature estimation
            estimated_precip = max(200, 2000 - abs(latitude) * 20)  # Rough precipitation estimation
            
            return {
                'source': 'estimated',
                'average_temperature': estimated_temp,
                'annual_precipitation': estimated_precip,
                'growing_season_length': max(100, 300 - abs(latitude) * 3),
                'climate_factor': 1.0,
                'soil_factor': 1.0,
                'confidence': 0.3
            }
            
        except Exception as e:
            logger.error(f"Error fetching external climate data: {str(e)}")
            return None
    
    async def _find_similar_species_parameters(self, species_name: str) -> Optional[Dict]:
        """Find parameters for similar species"""
        try:
            # Simple similarity matching based on common tree types
            species_lower = species_name.lower()
            
            # Define similarity groups
            similarity_groups = {
                'oak': ['oak', 'beech', 'maple'],
                'pine': ['pine', 'spruce', 'fir', 'cedar'],
                'eucalyptus': ['eucalyptus', 'acacia'],
                'birch': ['birch', 'poplar', 'aspen'],
            }
            
            # Find which group the species might belong to
            target_group = None
            for group_key, group_species in similarity_groups.items():
                if any(similar in species_lower for similar in group_species):
                    target_group = group_key
                    break
            
            if target_group:
                # Get a representative species from the group
                representative_species = await self.search_species_fuzzy(target_group, 1)
                if representative_species:
                    species = representative_species[0]
                    return {
                        'source': 'similarity_match',
                        'reference_species': species.name,
                        'allometric_a': species.allometric_a,
                        'allometric_b': species.allometric_b,
                        'allometric_c': species.allometric_c,
                        'allometric_d': species.allometric_d,
                        'wood_density': species.wood_density,
                        'carbon_content': species.carbon_content,
                        'annual_growth_rate': species.annual_growth_rate,
                        'confidence': 0.5
                    }
            
            # Fallback to default values
            return {
                'source': 'default_values',
                'allometric_a': 0.0673,
                'allometric_b': 2.085,
                'allometric_c': 0.756,
                'allometric_d': 0.0,
                'wood_density': 0.5,
                'carbon_content': 0.47,
                'annual_growth_rate': 0.02,
                'confidence': 0.2
            }
            
        except Exception as e:
            logger.error(f"Error finding similar species parameters: {str(e)}")
            return None


# Global instance for easy access
search_service = ForestDataSearchService()
