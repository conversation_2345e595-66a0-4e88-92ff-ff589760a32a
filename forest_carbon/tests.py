"""
Comprehensive tests for the Forest Carbon Sequestration system
"""

import json
import asyncio
from decimal import Decimal
from django.test import TestCase, TransactionTestCase
from django.contrib.auth.models import User
from django.urls import reverse
from rest_framework.test import APITestCase
from unittest.mock import patch, MagicMock

from .models import (
    TreeSpecies, GeographicRegion, ConversationSession,
    ForestData, CarbonCalculationResult, AgentInteraction, UserMessage
)
from .calculations import CarbonCalculator, ForestParameters, validate_forest_parameters
from .services import CarbonCalculationService, ConversationService
from .validators import ForestDataValidator, ValidationError as ForestValidationError
from .agents import OrchestratorAgent, InputExtractionAgent, SearchEngineAgent, CalculationAgent


class CarbonCalculationTests(TestCase):
    """Test carbon calculation mathematical functions"""

    def setUp(self):
        self.test_params = ForestParameters(
            area_hectares=10.0,
            tree_density=500.0,
            average_dbh=25.0,
            average_height=15.0,
            forest_age=20,
            allometric_a=0.0673,
            allometric_b=2.085,
            allometric_c=0.756,
            wood_density=0.5,
            carbon_content=0.47,
            annual_growth_rate=0.02,
            climate_factor=1.0,
            soil_factor=1.0
        )

    def test_individual_tree_biomass_calculation(self):
        """Test individual tree biomass calculation"""
        biomass = CarbonCalculator.calculate_individual_tree_biomass(
            dbh=25.0, height=15.0, wood_density=0.5,
            allometric_a=0.0673, allometric_b=2.085, allometric_c=0.756
        )

        self.assertGreater(biomass, 0)
        self.assertIsInstance(biomass, float)

        # Test edge cases
        zero_biomass = CarbonCalculator.calculate_individual_tree_biomass(
            dbh=0, height=15.0, wood_density=0.5
        )
        self.assertEqual(zero_biomass, 0.0)

    def test_above_ground_biomass_calculation(self):
        """Test above-ground biomass calculation"""
        biomass = CarbonCalculator.calculate_above_ground_biomass(
            area_hectares=10.0,
            tree_density=500.0,
            average_dbh=25.0,
            average_height=15.0,
            wood_density=0.5
        )

        self.assertGreater(biomass, 0)
        self.assertIsInstance(biomass, float)

    def test_below_ground_biomass_calculation(self):
        """Test below-ground biomass calculation"""
        above_ground = 100.0
        below_ground = CarbonCalculator.calculate_below_ground_biomass(above_ground)

        self.assertGreater(below_ground, 0)
        self.assertEqual(below_ground, above_ground * CarbonCalculator.ROOT_TO_SHOOT_RATIO)

    def test_carbon_content_calculation(self):
        """Test carbon content calculation"""
        total_biomass = 100.0
        carbon = CarbonCalculator.calculate_carbon_content(total_biomass, 0.47)

        self.assertEqual(carbon, 47.0)

    def test_annual_sequestration_calculation(self):
        """Test annual sequestration calculation"""
        current_carbon = 100.0
        annual_seq = CarbonCalculator.calculate_annual_sequestration(
            current_carbon, 0.02, 1.0, 1.0
        )

        expected = 100.0 * 0.02 * CarbonCalculator.CO2_TO_C_RATIO
        self.assertAlmostEqual(annual_seq, expected, places=2)

    def test_complete_calculation(self):
        """Test complete forest carbon calculation"""
        result = CarbonCalculator.calculate_forest_carbon_sequestration(self.test_params)

        self.assertGreater(result.total_biomass, 0)
        self.assertGreater(result.above_ground_biomass, 0)
        self.assertGreater(result.below_ground_biomass, 0)
        self.assertGreater(result.total_carbon, 0)
        self.assertGreater(result.annual_sequestration, 0)
        self.assertIsInstance(result.calculation_details, dict)

    def test_parameter_validation(self):
        """Test forest parameter validation"""
        # Valid parameters
        is_valid, error = validate_forest_parameters(self.test_params)
        self.assertTrue(is_valid)
        self.assertIsNone(error)

        # Invalid parameters
        invalid_params = ForestParameters(
            area_hectares=-1.0,  # Invalid negative area
            tree_density=500.0,
            average_dbh=25.0,
            average_height=15.0,
            forest_age=20
        )

        is_valid, error = validate_forest_parameters(invalid_params)
        self.assertFalse(is_valid)
        self.assertIsNotNone(error)


class ModelTests(TestCase):
    """Test Django models"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        self.species = TreeSpecies.objects.create(
            name='Test Oak',
            scientific_name='Quercus testus',
            wood_density=0.65,
            carbon_content=0.47
        )

        self.region = GeographicRegion.objects.create(
            name='Test Region',
            country='Test Country',
            latitude=40.0,
            longitude=-75.0,
            average_temperature=15.0,
            annual_precipitation=1000.0,
            growing_season_length=180
        )

    def test_tree_species_creation(self):
        """Test TreeSpecies model creation"""
        self.assertEqual(str(self.species), 'Test Oak')
        self.assertEqual(self.species.wood_density, 0.65)
        self.assertEqual(self.species.carbon_content, 0.47)

    def test_geographic_region_creation(self):
        """Test GeographicRegion model creation"""
        self.assertEqual(str(self.region), 'Test Region, Test Country')
        self.assertEqual(self.region.latitude, 40.0)
        self.assertEqual(self.region.longitude, -75.0)

    def test_conversation_session_creation(self):
        """Test ConversationSession model creation"""
        session = ConversationSession.objects.create(
            user=self.user,
            status='active'
        )

        self.assertEqual(session.user, self.user)
        self.assertEqual(session.status, 'active')
        self.assertEqual(session.current_step, 'species')
        self.assertEqual(session.completion_percentage, 0.0)

    def test_forest_data_creation(self):
        """Test ForestData model creation"""
        session = ConversationSession.objects.create(user=self.user)

        forest_data = ForestData.objects.create(
            session=session,
            species=self.species,
            region=self.region,
            area=10.0,
            area_unit='hectares',
            average_dbh=25.0,
            average_height=15.0,
            tree_density=500.0,
            forest_age=20
        )

        self.assertEqual(forest_data.get_area_in_hectares(), 10.0)
        self.assertEqual(forest_data.species, self.species)
        self.assertEqual(forest_data.region, self.region)

    def test_area_conversion(self):
        """Test area unit conversion"""
        session = ConversationSession.objects.create(user=self.user)

        # Test acres to hectares conversion
        forest_data = ForestData.objects.create(
            session=session,
            species=self.species,
            region=self.region,
            area=10.0,
            area_unit='acres',
            average_dbh=25.0,
            average_height=15.0,
            tree_density=500.0,
            forest_age=20
        )

        hectares = forest_data.get_area_in_hectares()
        expected_hectares = 10.0 * 0.404686
        self.assertAlmostEqual(hectares, expected_hectares, places=4)


class ValidatorTests(TestCase):
    """Test input validation functions"""

    def test_species_name_validation(self):
        """Test species name validation"""
        # Valid names
        valid, error = ForestDataValidator.validate_species_name("Oak")
        self.assertTrue(valid)
        self.assertIsNone(error)

        valid, error = ForestDataValidator.validate_species_name("Douglas Fir")
        self.assertTrue(valid)

        # Invalid names
        valid, error = ForestDataValidator.validate_species_name("")
        self.assertFalse(valid)
        self.assertIsInstance(error, ForestValidationError)

        valid, error = ForestDataValidator.validate_species_name("A")
        self.assertFalse(valid)

        valid, error = ForestDataValidator.validate_species_name("Oak123")
        self.assertFalse(valid)

    def test_location_validation(self):
        """Test location validation"""
        # Valid locations
        valid, error = ForestDataValidator.validate_location("California, USA")
        self.assertTrue(valid)

        # Invalid locations
        valid, error = ForestDataValidator.validate_location("")
        self.assertFalse(valid)

        valid, error = ForestDataValidator.validate_location("A")
        self.assertFalse(valid)

    def test_area_validation(self):
        """Test area validation"""
        # Valid areas
        valid, error, value = ForestDataValidator.validate_area("10.5", "hectares")
        self.assertTrue(valid)
        self.assertEqual(value, 10.5)

        valid, error, value = ForestDataValidator.validate_area(25.0, "acres")
        self.assertTrue(valid)

        # Invalid areas
        valid, error, value = ForestDataValidator.validate_area("-5", "hectares")
        self.assertFalse(valid)

        valid, error, value = ForestDataValidator.validate_area("abc", "hectares")
        self.assertFalse(valid)

        valid, error, value = ForestDataValidator.validate_area("10", "invalid_unit")
        self.assertFalse(valid)

    def test_tree_measurements_validation(self):
        """Test tree measurements validation"""
        # Valid measurements
        valid, error, values = ForestDataValidator.validate_tree_measurements(
            "25.5", "15.0", "500"
        )
        self.assertTrue(valid)
        self.assertEqual(values['dbh'], 25.5)
        self.assertEqual(values['height'], 15.0)
        self.assertEqual(values['density'], 500.0)

        # Invalid measurements
        valid, error, values = ForestDataValidator.validate_tree_measurements(
            "-5", "15.0", "500"
        )
        self.assertFalse(valid)

        valid, error, values = ForestDataValidator.validate_tree_measurements(
            "abc", "15.0", "500"
        )
        self.assertFalse(valid)

    def test_complete_data_validation(self):
        """Test complete forest data validation"""
        valid_data = {
            'species_name': 'Oak',
            'location': 'California, USA',
            'area': '10.5',
            'area_unit': 'hectares',
            'dbh': '25.0',
            'height': '15.0',
            'density': '500',
            'age': '20',
            'management_type': 'natural'
        }

        is_valid, errors, validated = ForestDataValidator.validate_complete_forest_data(valid_data)
        self.assertTrue(is_valid)
        self.assertEqual(len(errors), 0)
        self.assertIsNotNone(validated)

        # Test with invalid data
        invalid_data = valid_data.copy()
        invalid_data['area'] = '-5'

        is_valid, errors, validated = ForestDataValidator.validate_complete_forest_data(invalid_data)
        self.assertFalse(is_valid)
        self.assertGreater(len(errors), 0)


class ServiceTests(TransactionTestCase):
    """Test service layer functions"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        self.species = TreeSpecies.objects.create(
            name='Test Oak',
            scientific_name='Quercus testus',
            wood_density=0.65,
            carbon_content=0.47,
            allometric_a=0.0673,
            allometric_b=2.085,
            allometric_c=0.756
        )

        self.region = GeographicRegion.objects.create(
            name='Test Region',
            country='Test Country',
            latitude=40.0,
            longitude=-75.0,
            average_temperature=15.0,
            annual_precipitation=1000.0,
            growing_season_length=180,
            climate_factor=1.0,
            soil_factor=1.0
        )

    def test_conversation_service_create_session(self):
        """Test conversation session creation"""
        session = ConversationService.create_session(user=self.user)

        self.assertEqual(session.user, self.user)
        self.assertEqual(session.status, 'active')
        self.assertEqual(session.current_step, 'species')
        self.assertEqual(session.completion_percentage, 0.0)

    def test_conversation_service_update_session(self):
        """Test session data update"""
        session = ConversationService.create_session(user=self.user)

        test_data = {'species_name': 'Oak'}
        updated_session = ConversationService.update_session_data(
            session, 'species', test_data
        )

        self.assertEqual(updated_session.collected_data['species'], test_data)
        self.assertEqual(updated_session.current_step, 'location')
        self.assertGreater(updated_session.completion_percentage, 0)

    def test_conversation_service_progress(self):
        """Test session progress tracking"""
        session = ConversationService.create_session(user=self.user)
        progress = ConversationService.get_session_progress(session)

        self.assertEqual(progress['session_id'], str(session.session_id))
        self.assertEqual(progress['current_step'], 'species')
        self.assertEqual(progress['completion_percentage'], 0.0)
        self.assertIsInstance(progress['steps_remaining'], list)

    def test_carbon_calculation_service(self):
        """Test carbon calculation service"""
        session = ConversationSession.objects.create(user=self.user)

        forest_data = ForestData.objects.create(
            session=session,
            species=self.species,
            region=self.region,
            area=10.0,
            area_unit='hectares',
            average_dbh=25.0,
            average_height=15.0,
            tree_density=500.0,
            forest_age=20
        )

        # Test parameter creation
        params = CarbonCalculationService.create_forest_parameters_from_data(forest_data)

        self.assertEqual(params.area_hectares, 10.0)
        self.assertEqual(params.average_dbh, 25.0)
        self.assertEqual(params.average_height, 15.0)
        self.assertEqual(params.tree_density, 500.0)
        self.assertEqual(params.allometric_a, self.species.allometric_a)
        self.assertEqual(params.climate_factor, self.region.climate_factor)


class APITests(APITestCase):
    """Test API endpoints"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        # Create test data
        self.species = TreeSpecies.objects.create(
            name='Test Oak',
            scientific_name='Quercus testus',
            wood_density=0.65,
            carbon_content=0.47
        )

        self.region = GeographicRegion.objects.create(
            name='Test Region',
            country='Test Country',
            latitude=40.0,
            longitude=-75.0,
            average_temperature=15.0,
            annual_precipitation=1000.0,
            growing_season_length=180
        )

    def test_start_conversation_endpoint(self):
        """Test starting a new conversation"""
        url = '/api/forest-carbon/start-conversation'
        data = {'user_id': self.user.id}

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, 200)
        response_data = response.json()
        self.assertIn('session_id', response_data)
        self.assertIn('message', response_data)
        self.assertEqual(response_data['status'], 'active')

    def test_send_message_endpoint(self):
        """Test sending a message in conversation"""
        # First create a session
        session = ConversationSession.objects.create(
            user=self.user,
            status='active'
        )

        url = '/api/forest-carbon/send-message'
        data = {
            'session_id': str(session.session_id),
            'message': 'I have oak trees in my forest'
        }

        # Mock the orchestrator to avoid actual agent calls in tests
        with patch('forest_carbon.api.ForestCarbonController.orchestrator') as mock_orchestrator:
            mock_orchestrator.process_user_message.return_value = {
                'response_type': 'question',
                'message': 'Great! Where is your forest located?',
                'next_action': 'await_location',
                'data_status': {'species': {'species_name': 'oak'}},
                'completion_status': 0.2
            }

            response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, 200)
        response_data = response.json()
        self.assertEqual(response_data['response_type'], 'question')
        self.assertIn('message', response_data)

    def test_session_status_endpoint(self):
        """Test getting session status"""
        session = ConversationSession.objects.create(
            user=self.user,
            status='active',
            current_step='location',
            completion_percentage=25.0,
            collected_data={'species': {'species_name': 'oak'}}
        )

        url = f'/api/forest-carbon/session/{session.session_id}/status'
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        response_data = response.json()
        self.assertEqual(response_data['session_id'], str(session.session_id))
        self.assertEqual(response_data['status'], 'active')
        self.assertEqual(response_data['current_step'], 'location')
        self.assertEqual(response_data['completion_percentage'], 25.0)

    def test_session_messages_endpoint(self):
        """Test getting session messages"""
        session = ConversationSession.objects.create(
            user=self.user,
            status='active'
        )

        # Create some test messages
        UserMessage.objects.create(
            session=session,
            message_type='user',
            content='I have oak trees'
        )

        UserMessage.objects.create(
            session=session,
            message_type='agent',
            content='Great! Where is your forest located?',
            agent_name='OrchestratorAgent'
        )

        url = f'/api/forest-carbon/session/{session.session_id}/messages'
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        response_data = response.json()
        self.assertEqual(response_data['session_id'], str(session.session_id))
        self.assertEqual(len(response_data['messages']), 2)
        self.assertEqual(response_data['total_messages'], 2)

    def test_restart_session_endpoint(self):
        """Test restarting a session"""
        session = ConversationSession.objects.create(
            user=self.user,
            status='active',
            current_step='location',
            completion_percentage=50.0,
            collected_data={'species': {'species_name': 'oak'}}
        )

        url = f'/api/forest-carbon/session/{session.session_id}/restart'
        response = self.client.post(url)

        self.assertEqual(response.status_code, 200)

        # Check that session was reset
        session.refresh_from_db()
        self.assertEqual(session.current_step, 'species')
        self.assertEqual(session.completion_percentage, 0.0)
        self.assertEqual(session.collected_data, {})

    def test_list_sessions_endpoint(self):
        """Test listing sessions"""
        # Create test sessions
        ConversationSession.objects.create(user=self.user, status='active')
        ConversationSession.objects.create(user=self.user, status='completed')

        url = '/api/forest-carbon/sessions'
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        response_data = response.json()
        self.assertGreaterEqual(len(response_data['sessions']), 2)

        # Test filtering by user
        url = f'/api/forest-carbon/sessions?user_id={self.user.id}'
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        response_data = response.json()
        self.assertEqual(len(response_data['sessions']), 2)

    def test_calculation_result_endpoint(self):
        """Test getting calculation results"""
        session = ConversationSession.objects.create(user=self.user)

        forest_data = ForestData.objects.create(
            session=session,
            species=self.species,
            region=self.region,
            area=10.0,
            average_dbh=25.0,
            average_height=15.0,
            tree_density=500.0,
            forest_age=20
        )

        calc_result = CarbonCalculationResult.objects.create(
            forest_data=forest_data,
            session=session,
            status='completed',
            total_biomass=100.0,
            total_carbon=47.0,
            annual_sequestration=5.5
        )

        url = f'/api/forest-carbon/calculation/{calc_result.calculation_id}'
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        response_data = response.json()
        self.assertEqual(response_data['calculation_id'], str(calc_result.calculation_id))
        self.assertEqual(response_data['status'], 'completed')
        self.assertEqual(response_data['total_biomass'], 100.0)
        self.assertEqual(response_data['total_carbon'], 47.0)
        self.assertEqual(response_data['annual_sequestration'], 5.5)


class IntegrationTests(TransactionTestCase):
    """Integration tests for the complete workflow"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        # Create test species and region
        TreeSpecies.objects.create(
            name='Oak',
            scientific_name='Quercus spp.',
            wood_density=0.65,
            carbon_content=0.47,
            allometric_a=0.0673,
            allometric_b=2.085,
            allometric_c=0.756,
            annual_growth_rate=0.015
        )

        GeographicRegion.objects.create(
            name='California',
            country='United States',
            latitude=36.0,
            longitude=-119.0,
            average_temperature=16.0,
            annual_precipitation=600.0,
            growing_season_length=200,
            climate_factor=1.0,
            soil_factor=1.0
        )

    def test_complete_conversation_workflow(self):
        """Test complete conversation workflow from start to calculation"""
        # This would be a comprehensive integration test
        # For now, we'll test the basic flow

        # 1. Create session
        session = ConversationService.create_session(user=self.user)
        self.assertEqual(session.current_step, 'species')

        # 2. Update with species data
        session = ConversationService.update_session_data(
            session, 'species', {'species_name': 'Oak'}
        )
        self.assertEqual(session.current_step, 'location')

        # 3. Update with location data
        session = ConversationService.update_session_data(
            session, 'location', {'location_name': 'California'}
        )
        self.assertEqual(session.current_step, 'area')

        # 4. Update with area data
        session = ConversationService.update_session_data(
            session, 'area', {'area': 10.0, 'unit': 'hectares'}
        )
        self.assertEqual(session.current_step, 'tree_measurements')

        # 5. Update with measurements
        session = ConversationService.update_session_data(
            session, 'tree_measurements', {
                'dbh': 25.0, 'height': 15.0, 'density': 500.0
            }
        )
        self.assertEqual(session.current_step, 'forest_characteristics')

        # 6. Update with characteristics
        session = ConversationService.update_session_data(
            session, 'forest_characteristics', {
                'age': 20, 'management': 'natural'
            }
        )
        self.assertEqual(session.current_step, 'confirmation')

        # Check that all data was collected
        self.assertIn('species', session.collected_data)
        self.assertIn('location', session.collected_data)
        self.assertIn('area', session.collected_data)
        self.assertIn('tree_measurements', session.collected_data)
        self.assertIn('forest_characteristics', session.collected_data)

        # Check completion percentage
        self.assertGreater(session.completion_percentage, 80.0)
