"""
Management command to populate the database with initial forest species and regional data
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from forest_carbon.models import TreeSpecies, GeographicRegion


class Command(BaseCommand):
    help = 'Populate database with initial tree species and geographic region data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--species-only',
            action='store_true',
            help='Only populate tree species data',
        )
        parser.add_argument(
            '--regions-only',
            action='store_true',
            help='Only populate geographic regions data',
        )

    def handle(self, *args, **options):
        with transaction.atomic():
            if not options['regions_only']:
                self.populate_tree_species()
            
            if not options['species_only']:
                self.populate_geographic_regions()
        
        self.stdout.write(
            self.style.SUCCESS('Successfully populated forest data')
        )

    def populate_tree_species(self):
        """Populate tree species with their allometric parameters"""
        
        species_data = [
            {
                'name': 'Oak',
                'scientific_name': 'Quercus spp.',
                'allometric_a': 0.0673,
                'allometric_b': 2.085,
                'allometric_c': 0.756,
                'allometric_d': 0.0,
                'wood_density': 0.65,
                'carbon_content': 0.47,
                'annual_growth_rate': 0.015,
            },
            {
                'name': 'Pine',
                'scientific_name': 'Pinus spp.',
                'allometric_a': 0.0481,
                'allometric_b': 2.204,
                'allometric_c': 0.827,
                'allometric_d': 0.0,
                'wood_density': 0.45,
                'carbon_content': 0.48,
                'annual_growth_rate': 0.025,
            },
            {
                'name': 'Eucalyptus',
                'scientific_name': 'Eucalyptus spp.',
                'allometric_a': 0.0889,
                'allometric_b': 1.945,
                'allometric_c': 0.635,
                'allometric_d': 0.0,
                'wood_density': 0.55,
                'carbon_content': 0.46,
                'annual_growth_rate': 0.035,
            },
            {
                'name': 'Maple',
                'scientific_name': 'Acer spp.',
                'allometric_a': 0.0712,
                'allometric_b': 2.024,
                'allometric_c': 0.789,
                'allometric_d': 0.0,
                'wood_density': 0.62,
                'carbon_content': 0.47,
                'annual_growth_rate': 0.018,
            },
            {
                'name': 'Birch',
                'scientific_name': 'Betula spp.',
                'allometric_a': 0.0534,
                'allometric_b': 2.156,
                'allometric_c': 0.812,
                'allometric_d': 0.0,
                'wood_density': 0.52,
                'carbon_content': 0.48,
                'annual_growth_rate': 0.022,
            },
            {
                'name': 'Spruce',
                'scientific_name': 'Picea spp.',
                'allometric_a': 0.0445,
                'allometric_b': 2.267,
                'allometric_c': 0.845,
                'allometric_d': 0.0,
                'wood_density': 0.42,
                'carbon_content': 0.49,
                'annual_growth_rate': 0.020,
            },
            {
                'name': 'Fir',
                'scientific_name': 'Abies spp.',
                'allometric_a': 0.0512,
                'allometric_b': 2.189,
                'allometric_c': 0.798,
                'allometric_d': 0.0,
                'wood_density': 0.38,
                'carbon_content': 0.48,
                'annual_growth_rate': 0.018,
            },
            {
                'name': 'Beech',
                'scientific_name': 'Fagus spp.',
                'allometric_a': 0.0698,
                'allometric_b': 2.045,
                'allometric_c': 0.734,
                'allometric_d': 0.0,
                'wood_density': 0.68,
                'carbon_content': 0.46,
                'annual_growth_rate': 0.016,
            },
            {
                'name': 'Poplar',
                'scientific_name': 'Populus spp.',
                'allometric_a': 0.0623,
                'allometric_b': 2.112,
                'allometric_c': 0.823,
                'allometric_d': 0.0,
                'wood_density': 0.35,
                'carbon_content': 0.47,
                'annual_growth_rate': 0.030,
            },
            {
                'name': 'Cedar',
                'scientific_name': 'Cedrus spp.',
                'allometric_a': 0.0567,
                'allometric_b': 2.134,
                'allometric_c': 0.756,
                'allometric_d': 0.0,
                'wood_density': 0.48,
                'carbon_content': 0.48,
                'annual_growth_rate': 0.019,
            },
        ]

        for species_info in species_data:
            species, created = TreeSpecies.objects.get_or_create(
                name=species_info['name'],
                defaults=species_info
            )
            if created:
                self.stdout.write(f"Created species: {species.name}")
            else:
                self.stdout.write(f"Species already exists: {species.name}")

    def populate_geographic_regions(self):
        """Populate geographic regions with climate data"""
        
        regions_data = [
            {
                'name': 'Pacific Northwest',
                'country': 'United States',
                'latitude': 45.5,
                'longitude': -122.0,
                'average_temperature': 12.0,
                'annual_precipitation': 1200.0,
                'growing_season_length': 180,
                'climate_factor': 1.1,
                'soil_factor': 1.0,
            },
            {
                'name': 'Northeastern United States',
                'country': 'United States',
                'latitude': 42.0,
                'longitude': -75.0,
                'average_temperature': 8.5,
                'annual_precipitation': 1000.0,
                'growing_season_length': 160,
                'climate_factor': 0.95,
                'soil_factor': 1.0,
            },
            {
                'name': 'Southeastern United States',
                'country': 'United States',
                'latitude': 33.0,
                'longitude': -84.0,
                'average_temperature': 18.0,
                'annual_precipitation': 1300.0,
                'growing_season_length': 220,
                'climate_factor': 1.15,
                'soil_factor': 0.9,
            },
            {
                'name': 'Central Europe',
                'country': 'Germany',
                'latitude': 51.0,
                'longitude': 10.0,
                'average_temperature': 9.0,
                'annual_precipitation': 800.0,
                'growing_season_length': 170,
                'climate_factor': 0.9,
                'soil_factor': 1.1,
            },
            {
                'name': 'Scandinavia',
                'country': 'Sweden',
                'latitude': 62.0,
                'longitude': 15.0,
                'average_temperature': 3.0,
                'annual_precipitation': 600.0,
                'growing_season_length': 120,
                'climate_factor': 0.7,
                'soil_factor': 0.8,
            },
            {
                'name': 'Eastern Australia',
                'country': 'Australia',
                'latitude': -33.0,
                'longitude': 151.0,
                'average_temperature': 18.5,
                'annual_precipitation': 1200.0,
                'growing_season_length': 300,
                'climate_factor': 1.2,
                'soil_factor': 0.85,
            },
            {
                'name': 'British Columbia',
                'country': 'Canada',
                'latitude': 54.0,
                'longitude': -125.0,
                'average_temperature': 4.5,
                'annual_precipitation': 1400.0,
                'growing_season_length': 140,
                'climate_factor': 0.85,
                'soil_factor': 1.0,
            },
            {
                'name': 'Amazon Basin',
                'country': 'Brazil',
                'latitude': -3.0,
                'longitude': -60.0,
                'average_temperature': 26.0,
                'annual_precipitation': 2300.0,
                'growing_season_length': 365,
                'climate_factor': 1.4,
                'soil_factor': 0.7,
            },
            {
                'name': 'Siberian Taiga',
                'country': 'Russia',
                'latitude': 60.0,
                'longitude': 100.0,
                'average_temperature': -5.0,
                'annual_precipitation': 400.0,
                'growing_season_length': 100,
                'climate_factor': 0.6,
                'soil_factor': 0.7,
            },
            {
                'name': 'Mediterranean',
                'country': 'Spain',
                'latitude': 40.0,
                'longitude': -4.0,
                'average_temperature': 15.0,
                'annual_precipitation': 500.0,
                'growing_season_length': 200,
                'climate_factor': 0.8,
                'soil_factor': 0.9,
            },
        ]

        for region_info in regions_data:
            region, created = GeographicRegion.objects.get_or_create(
                name=region_info['name'],
                defaults=region_info
            )
            if created:
                self.stdout.write(f"Created region: {region.name}")
            else:
                self.stdout.write(f"Region already exists: {region.name}")
