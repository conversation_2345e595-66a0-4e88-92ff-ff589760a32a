"""
Input Validation and Error Handling for Forest Carbon Sequestration

This module provides comprehensive validation for all user inputs and
robust error handling throughout the system.
"""

import re
import logging
from typing import Dict, List, Optional, Tuple, Any, Union
from decimal import Decimal, InvalidOperation
from django.core.exceptions import ValidationError
from django.core.validators import validate_email

logger = logging.getLogger(__name__)


class ValidationError(Exception):
    """Custom validation error with detailed information"""
    
    def __init__(self, message: str, field: str = None, code: str = None, suggestions: List[str] = None):
        self.message = message
        self.field = field
        self.code = code
        self.suggestions = suggestions or []
        super().__init__(self.message)
    
    def to_dict(self) -> Dict:
        return {
            'error': self.message,
            'field': self.field,
            'code': self.code,
            'suggestions': self.suggestions
        }


class ForestDataValidator:
    """
    Comprehensive validator for forest data inputs
    """
    
    # Validation ranges and constants
    VALID_RANGES = {
        'area_hectares': (0.01, 1000000),  # 0.01 hectares to 1M hectares
        'area_acres': (0.025, 2471054),    # 0.025 acres to 2.47M acres
        'dbh_cm': (1, 500),                # 1cm to 5m diameter
        'height_m': (0.5, 150),            # 0.5m to 150m height
        'tree_density': (1, 10000),        # 1 to 10,000 trees per hectare
        'forest_age': (1, 1000),           # 1 to 1000 years
        'latitude': (-90, 90),             # Valid latitude range
        'longitude': (-180, 180),          # Valid longitude range
        'temperature': (-50, 50),          # Temperature in Celsius
        'precipitation': (0, 10000),       # Annual precipitation in mm
        'wood_density': (0.1, 2.0),       # Wood density in g/cm³
        'carbon_content': (0.1, 0.8),     # Carbon content as fraction
        'growth_rate': (0, 0.5),          # Annual growth rate as fraction
    }
    
    AREA_UNITS = ['hectares', 'acres', 'ha', 'ac']
    MANAGEMENT_TYPES = ['natural', 'planted', 'managed', 'selective', 'clear-cut', 'sustainable']
    
    @classmethod
    def validate_species_name(cls, species_name: str) -> Tuple[bool, Optional[ValidationError]]:
        """
        Validate tree species name input
        
        Args:
            species_name: Species name to validate
            
        Returns:
            Tuple of (is_valid, error_or_none)
        """
        try:
            if not species_name or not isinstance(species_name, str):
                return False, ValidationError(
                    "Species name is required and must be text",
                    field="species_name",
                    code="required",
                    suggestions=["Provide a tree species name like 'Oak', 'Pine', or 'Eucalyptus'"]
                )
            
            species_name = species_name.strip()
            
            if len(species_name) < 2:
                return False, ValidationError(
                    "Species name must be at least 2 characters long",
                    field="species_name",
                    code="too_short",
                    suggestions=["Use common names like 'Oak' or scientific names like 'Quercus'"]
                )
            
            if len(species_name) > 100:
                return False, ValidationError(
                    "Species name is too long (maximum 100 characters)",
                    field="species_name",
                    code="too_long"
                )
            
            # Check for valid characters (letters, spaces, hyphens, periods)
            if not re.match(r'^[a-zA-Z\s\-\.]+$', species_name):
                return False, ValidationError(
                    "Species name can only contain letters, spaces, hyphens, and periods",
                    field="species_name",
                    code="invalid_characters",
                    suggestions=["Use only letters and common punctuation"]
                )
            
            return True, None
            
        except Exception as e:
            logger.error(f"Error validating species name: {str(e)}")
            return False, ValidationError(
                "Error validating species name",
                field="species_name",
                code="validation_error"
            )
    
    @classmethod
    def validate_location(cls, location: str) -> Tuple[bool, Optional[ValidationError]]:
        """
        Validate geographic location input
        
        Args:
            location: Location string to validate
            
        Returns:
            Tuple of (is_valid, error_or_none)
        """
        try:
            if not location or not isinstance(location, str):
                return False, ValidationError(
                    "Location is required and must be text",
                    field="location",
                    code="required",
                    suggestions=["Provide a location like 'California, USA' or coordinates"]
                )
            
            location = location.strip()
            
            if len(location) < 2:
                return False, ValidationError(
                    "Location must be at least 2 characters long",
                    field="location",
                    code="too_short"
                )
            
            if len(location) > 200:
                return False, ValidationError(
                    "Location is too long (maximum 200 characters)",
                    field="location",
                    code="too_long"
                )
            
            return True, None
            
        except Exception as e:
            logger.error(f"Error validating location: {str(e)}")
            return False, ValidationError(
                "Error validating location",
                field="location",
                code="validation_error"
            )
    
    @classmethod
    def validate_coordinates(cls, latitude: float, longitude: float) -> Tuple[bool, Optional[ValidationError]]:
        """
        Validate geographic coordinates
        
        Args:
            latitude: Latitude coordinate
            longitude: Longitude coordinate
            
        Returns:
            Tuple of (is_valid, error_or_none)
        """
        try:
            lat_min, lat_max = cls.VALID_RANGES['latitude']
            lon_min, lon_max = cls.VALID_RANGES['longitude']
            
            if not (lat_min <= latitude <= lat_max):
                return False, ValidationError(
                    f"Latitude must be between {lat_min} and {lat_max} degrees",
                    field="latitude",
                    code="out_of_range"
                )
            
            if not (lon_min <= longitude <= lon_max):
                return False, ValidationError(
                    f"Longitude must be between {lon_min} and {lon_max} degrees",
                    field="longitude",
                    code="out_of_range"
                )
            
            return True, None
            
        except Exception as e:
            logger.error(f"Error validating coordinates: {str(e)}")
            return False, ValidationError(
                "Error validating coordinates",
                field="coordinates",
                code="validation_error"
            )
    
    @classmethod
    def validate_area(cls, area: Union[str, float], unit: str = 'hectares') -> Tuple[bool, Optional[ValidationError], Optional[float]]:
        """
        Validate forest area input
        
        Args:
            area: Area value (string or number)
            unit: Area unit (hectares, acres, etc.)
            
        Returns:
            Tuple of (is_valid, error_or_none, parsed_value)
        """
        try:
            # Validate unit
            if unit.lower() not in cls.AREA_UNITS:
                return False, ValidationError(
                    f"Invalid area unit. Must be one of: {', '.join(cls.AREA_UNITS)}",
                    field="area_unit",
                    code="invalid_unit"
                ), None
            
            # Parse area value
            try:
                if isinstance(area, str):
                    # Remove common non-numeric characters
                    area_clean = re.sub(r'[^\d\.\-\+]', '', area.strip())
                    area_value = float(area_clean)
                else:
                    area_value = float(area)
            except (ValueError, TypeError):
                return False, ValidationError(
                    "Area must be a valid number",
                    field="area",
                    code="invalid_number",
                    suggestions=["Enter a number like '10.5' or '100'"]
                ), None
            
            # Validate range based on unit
            unit_key = 'area_hectares' if unit.lower() in ['hectares', 'ha'] else 'area_acres'
            min_val, max_val = cls.VALID_RANGES[unit_key]
            
            if area_value <= 0:
                return False, ValidationError(
                    "Area must be greater than 0",
                    field="area",
                    code="non_positive"
                ), None
            
            if not (min_val <= area_value <= max_val):
                return False, ValidationError(
                    f"Area must be between {min_val} and {max_val} {unit}",
                    field="area",
                    code="out_of_range"
                ), None
            
            return True, None, area_value
            
        except Exception as e:
            logger.error(f"Error validating area: {str(e)}")
            return False, ValidationError(
                "Error validating area",
                field="area",
                code="validation_error"
            ), None
    
    @classmethod
    def validate_tree_measurements(
        cls, 
        dbh: Union[str, float], 
        height: Union[str, float], 
        density: Union[str, float]
    ) -> Tuple[bool, Optional[ValidationError], Optional[Dict[str, float]]]:
        """
        Validate tree measurement inputs
        
        Args:
            dbh: Diameter at breast height in cm
            height: Tree height in meters
            density: Tree density per hectare
            
        Returns:
            Tuple of (is_valid, error_or_none, parsed_values_dict)
        """
        try:
            parsed_values = {}
            
            # Validate DBH
            try:
                dbh_value = float(str(dbh).strip())
                min_dbh, max_dbh = cls.VALID_RANGES['dbh_cm']
                if not (min_dbh <= dbh_value <= max_dbh):
                    return False, ValidationError(
                        f"DBH must be between {min_dbh} and {max_dbh} cm",
                        field="dbh",
                        code="out_of_range"
                    ), None
                parsed_values['dbh'] = dbh_value
            except (ValueError, TypeError):
                return False, ValidationError(
                    "DBH must be a valid number in centimeters",
                    field="dbh",
                    code="invalid_number",
                    suggestions=["Enter DBH in cm, e.g., '25.5' for 25.5 cm diameter"]
                ), None
            
            # Validate height
            try:
                height_value = float(str(height).strip())
                min_height, max_height = cls.VALID_RANGES['height_m']
                if not (min_height <= height_value <= max_height):
                    return False, ValidationError(
                        f"Height must be between {min_height} and {max_height} meters",
                        field="height",
                        code="out_of_range"
                    ), None
                parsed_values['height'] = height_value
            except (ValueError, TypeError):
                return False, ValidationError(
                    "Height must be a valid number in meters",
                    field="height",
                    code="invalid_number",
                    suggestions=["Enter height in meters, e.g., '15.5' for 15.5 meters"]
                ), None
            
            # Validate density
            try:
                density_value = float(str(density).strip())
                min_density, max_density = cls.VALID_RANGES['tree_density']
                if not (min_density <= density_value <= max_density):
                    return False, ValidationError(
                        f"Tree density must be between {min_density} and {max_density} trees per hectare",
                        field="density",
                        code="out_of_range"
                    ), None
                parsed_values['density'] = density_value
            except (ValueError, TypeError):
                return False, ValidationError(
                    "Tree density must be a valid number",
                    field="density",
                    code="invalid_number",
                    suggestions=["Enter number of trees per hectare, e.g., '500'"]
                ), None
            
            # Cross-validation: check if measurements are reasonable together
            if parsed_values['dbh'] > 100 and parsed_values['height'] < 10:
                return False, ValidationError(
                    "Large DBH with small height seems unrealistic",
                    field="measurements",
                    code="inconsistent_measurements",
                    suggestions=["Check if DBH and height measurements are correct"]
                ), None
            
            if parsed_values['density'] > 2000 and parsed_values['dbh'] > 50:
                return False, ValidationError(
                    "High tree density with large DBH seems unrealistic",
                    field="measurements",
                    code="inconsistent_measurements",
                    suggestions=["Large trees typically have lower density"]
                ), None
            
            return True, None, parsed_values
            
        except Exception as e:
            logger.error(f"Error validating tree measurements: {str(e)}")
            return False, ValidationError(
                "Error validating tree measurements",
                field="measurements",
                code="validation_error"
            ), None
    
    @classmethod
    def validate_forest_characteristics(
        cls, 
        age: Union[str, int], 
        management_type: str
    ) -> Tuple[bool, Optional[ValidationError], Optional[Dict[str, Any]]]:
        """
        Validate forest characteristics
        
        Args:
            age: Forest age in years
            management_type: Type of forest management
            
        Returns:
            Tuple of (is_valid, error_or_none, parsed_values_dict)
        """
        try:
            parsed_values = {}
            
            # Validate age
            try:
                age_value = int(str(age).strip())
                min_age, max_age = cls.VALID_RANGES['forest_age']
                if not (min_age <= age_value <= max_age):
                    return False, ValidationError(
                        f"Forest age must be between {min_age} and {max_age} years",
                        field="age",
                        code="out_of_range"
                    ), None
                parsed_values['age'] = age_value
            except (ValueError, TypeError):
                return False, ValidationError(
                    "Forest age must be a valid whole number",
                    field="age",
                    code="invalid_number",
                    suggestions=["Enter age in years, e.g., '25' for 25 years old"]
                ), None
            
            # Validate management type
            management_clean = management_type.strip().lower()
            if management_clean not in cls.MANAGEMENT_TYPES:
                return False, ValidationError(
                    f"Invalid management type. Must be one of: {', '.join(cls.MANAGEMENT_TYPES)}",
                    field="management_type",
                    code="invalid_choice",
                    suggestions=[f"Choose from: {', '.join(cls.MANAGEMENT_TYPES)}"]
                ), None
            
            parsed_values['management_type'] = management_clean
            
            return True, None, parsed_values
            
        except Exception as e:
            logger.error(f"Error validating forest characteristics: {str(e)}")
            return False, ValidationError(
                "Error validating forest characteristics",
                field="characteristics",
                code="validation_error"
            ), None
    
    @classmethod
    def validate_complete_forest_data(cls, data: Dict) -> Tuple[bool, List[ValidationError], Optional[Dict]]:
        """
        Validate complete forest data set
        
        Args:
            data: Dictionary containing all forest data
            
        Returns:
            Tuple of (is_valid, list_of_errors, validated_data)
        """
        errors = []
        validated_data = {}
        
        try:
            # Validate species
            if 'species_name' in data:
                is_valid, error = cls.validate_species_name(data['species_name'])
                if not is_valid:
                    errors.append(error)
                else:
                    validated_data['species_name'] = data['species_name'].strip()
            
            # Validate location
            if 'location' in data:
                is_valid, error = cls.validate_location(data['location'])
                if not is_valid:
                    errors.append(error)
                else:
                    validated_data['location'] = data['location'].strip()
            
            # Validate area
            if 'area' in data:
                unit = data.get('area_unit', 'hectares')
                is_valid, error, area_value = cls.validate_area(data['area'], unit)
                if not is_valid:
                    errors.append(error)
                else:
                    validated_data['area'] = area_value
                    validated_data['area_unit'] = unit
            
            # Validate measurements
            if all(key in data for key in ['dbh', 'height', 'density']):
                is_valid, error, measurements = cls.validate_tree_measurements(
                    data['dbh'], data['height'], data['density']
                )
                if not is_valid:
                    errors.append(error)
                else:
                    validated_data.update(measurements)
            
            # Validate characteristics
            if all(key in data for key in ['age', 'management_type']):
                is_valid, error, characteristics = cls.validate_forest_characteristics(
                    data['age'], data['management_type']
                )
                if not is_valid:
                    errors.append(error)
                else:
                    validated_data.update(characteristics)
            
            return len(errors) == 0, errors, validated_data if len(errors) == 0 else None
            
        except Exception as e:
            logger.error(f"Error in complete validation: {str(e)}")
            errors.append(ValidationError(
                "Error during validation process",
                code="validation_error"
            ))
            return False, errors, None


class ErrorHandler:
    """
    Centralized error handling for the forest carbon system
    """
    
    @staticmethod
    def handle_validation_errors(errors: List[ValidationError]) -> Dict:
        """
        Convert validation errors to API response format
        
        Args:
            errors: List of ValidationError objects
            
        Returns:
            Dictionary formatted for API response
        """
        return {
            'success': False,
            'error_type': 'validation_error',
            'errors': [error.to_dict() for error in errors],
            'message': 'Please correct the following validation errors:'
        }
    
    @staticmethod
    def handle_system_error(error: Exception, context: str = "") -> Dict:
        """
        Handle system/unexpected errors
        
        Args:
            error: Exception object
            context: Additional context about where the error occurred
            
        Returns:
            Dictionary formatted for API response
        """
        logger.error(f"System error in {context}: {str(error)}", exc_info=True)
        
        return {
            'success': False,
            'error_type': 'system_error',
            'message': 'An unexpected error occurred. Please try again.',
            'context': context
        }
    
    @staticmethod
    def handle_calculation_error(error: Exception, forest_data: Dict = None) -> Dict:
        """
        Handle calculation-specific errors
        
        Args:
            error: Exception object
            forest_data: Forest data that caused the error (optional)
            
        Returns:
            Dictionary formatted for API response
        """
        logger.error(f"Calculation error: {str(error)}", exc_info=True)
        
        return {
            'success': False,
            'error_type': 'calculation_error',
            'message': 'Error occurred during carbon sequestration calculation.',
            'suggestions': [
                'Check that all input values are reasonable',
                'Ensure species and location data are valid',
                'Try with different parameter values'
            ]
        }
