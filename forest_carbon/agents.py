"""
AutoGen Multi-Agent System for Forest Carbon Sequestration

This module implements specialized agents using the AutoGen framework for
coordinated forest carbon sequestration calculations and data collection.
"""

import json
import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.messages import TextMessage
from autogen_ext.models.ollama import OllamaChatCompletionClient
from autogen_core.models import ModelInfo

from .models import ConversationSession, TreeSpecies, GeographicRegion, ForestData
from .services import (
    CarbonCalculationService, ConversationService,
    AgentInteractionService, DataValidationService
)
from .validators import ForestDataValidator, ValidationError as ForestValidationError

logger = logging.getLogger(__name__)


class BaseForestAgent:
    """
    Base class for all forest carbon sequestration agents
    """
    
    def __init__(self, name: str, system_message: str, model_name: str = "qwen2.5:32b"):
        self.name = name
        self.system_message = system_message
        self.model_client = OllamaChatCompletionClient(
            model=model_name,
            model_info={
                "family": "qwen",
                "vision": False,
                "function_calling": True,
                "structured_output": True,
                "json_output": True,
            }
        )
        
        self.agent = AssistantAgent(
            name=self.name,
            model_client=self.model_client,
            system_message=self.system_message
        )
    
    async def log_interaction(
        self, 
        session: ConversationSession,
        receiver_agent: str,
        message_type: str,
        message_content: Dict,
        execution_time: Optional[float] = None,
        success: bool = True,
        error_details: str = ""
    ):
        """Log agent interaction"""
        return AgentInteractionService.log_interaction(
            session=session,
            sender_agent=self.name,
            receiver_agent=receiver_agent,
            message_type=message_type,
            message_content=message_content,
            execution_time=execution_time,
            success=success,
            error_details=error_details
        )


class InputExtractionAgent(BaseForestAgent):
    """
    Agent responsible for extracting and validating user inputs from conversations
    """
    
    def __init__(self):
        system_message = """
        You are an Input Extraction Agent specialized in extracting forest data from user conversations.
        Your role is to:
        1. Parse user messages to extract relevant forest information
        2. Identify tree species, geographic locations, measurements, and other forest parameters
        3. Validate extracted data and request clarification when needed
        4. Structure the extracted data in a standardized format
        
        Always respond with structured JSON containing:
        - extracted_data: Dictionary of extracted parameters
        - confidence_level: Float between 0-1 indicating extraction confidence
        - missing_data: List of missing required parameters
        - clarification_needed: Boolean indicating if clarification is required
        - next_question: String with the next question to ask the user (if needed)
        
        Be conversational and helpful while maintaining accuracy in data extraction.
        """
        super().__init__("InputExtractionAgent", system_message)
    
    async def extract_data_from_message(
        self, 
        session: ConversationSession,
        user_message: str,
        current_step: str
    ) -> Dict:
        """
        Extract forest data from user message based on current conversation step
        """
        start_time = datetime.now()
        
        try:
            # Prepare context for the agent
            context = {
                "current_step": current_step,
                "session_data": session.collected_data,
                "user_message": user_message
            }
            
            prompt = f"""
            Extract forest data from the user message for step '{current_step}'.
            
            Context: {json.dumps(context, indent=2)}
            
            User message: "{user_message}"
            
            Please extract relevant data and provide structured response.
            """
            
            message = TextMessage(content=prompt, source="user")
            response = await self.agent.run(task=message)
            
            # Parse response
            if hasattr(response, 'content'):
                response_content = response.content
            else:
                response_content = str(response)
            
            # Try to parse JSON response
            try:
                extracted_data = json.loads(response_content)
            except json.JSONDecodeError:
                # Fallback: create basic structure
                extracted_data = {
                    "extracted_data": {"raw_message": user_message},
                    "confidence_level": 0.5,
                    "missing_data": [],
                    "clarification_needed": True,
                    "next_question": "Could you please provide more specific information?"
                }
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # Log interaction
            await self.log_interaction(
                session=session,
                receiver_agent="ConversationSystem",
                message_type="response",
                message_content=extracted_data,
                execution_time=execution_time,
                success=True
            )
            
            return extracted_data
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            error_msg = str(e)
            
            await self.log_interaction(
                session=session,
                receiver_agent="ConversationSystem",
                message_type="error",
                message_content={"error": error_msg},
                execution_time=execution_time,
                success=False,
                error_details=error_msg
            )
            
            logger.error(f"Error in input extraction: {error_msg}")
            raise


class SearchEngineAgent(BaseForestAgent):
    """
    Agent responsible for searching and retrieving forest parameters and coefficients
    """
    
    def __init__(self):
        system_message = """
        You are a Search Engine Agent specialized in retrieving forest and climate data.
        Your role is to:
        1. Search for species-specific parameters (allometric coefficients, wood density, etc.)
        2. Retrieve regional climate data and adjustment factors
        3. Find missing parameters required for carbon calculations
        4. Provide data sources and confidence levels for retrieved information
        
        Always respond with structured JSON containing:
        - found_data: Dictionary of retrieved parameters
        - data_sources: List of sources for the retrieved data
        - confidence_level: Float between 0-1 indicating data reliability
        - search_status: String indicating success/partial/failed
        - recommendations: List of recommended default values if data not found
        """
        super().__init__("SearchEngineAgent", system_message)
    
    async def search_species_parameters(
        self, 
        session: ConversationSession,
        species_name: str
    ) -> Dict:
        """
        Search for species-specific parameters
        """
        start_time = datetime.now()
        
        try:
            # First try to find in database
            is_valid, error_msg, species = DataValidationService.validate_species_data(species_name)
            
            if is_valid and species:
                # Found in database
                found_data = {
                    "species_id": species.id,
                    "name": species.name,
                    "scientific_name": species.scientific_name,
                    "allometric_a": species.allometric_a,
                    "allometric_b": species.allometric_b,
                    "allometric_c": species.allometric_c,
                    "allometric_d": species.allometric_d,
                    "wood_density": species.wood_density,
                    "carbon_content": species.carbon_content,
                    "annual_growth_rate": species.annual_growth_rate
                }
                
                result = {
                    "found_data": found_data,
                    "data_sources": ["Internal Database"],
                    "confidence_level": 0.9,
                    "search_status": "success",
                    "recommendations": []
                }
            else:
                # Not found in database, use agent to search for similar species or defaults
                prompt = f"""
                Search for forest parameters for species: "{species_name}"
                
                The species was not found in our database. Please provide:
                1. Similar species that might have comparable parameters
                2. Recommended default values based on tree type (coniferous/deciduous)
                3. Typical ranges for allometric parameters
                
                Provide structured response with recommendations.
                """
                
                message = TextMessage(content=prompt, source="user")
                response = await self.agent.run(task=message)
                
                # Parse response
                if hasattr(response, 'content'):
                    response_content = response.content
                else:
                    response_content = str(response)
                
                try:
                    result = json.loads(response_content)
                except json.JSONDecodeError:
                    # Fallback with default values
                    result = {
                        "found_data": {},
                        "data_sources": ["Default Values"],
                        "confidence_level": 0.3,
                        "search_status": "partial",
                        "recommendations": [
                            "Use default allometric parameters for mixed forest",
                            "Consider wood density of 0.5 g/cm³",
                            "Use carbon content of 47%"
                        ]
                    }
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            await self.log_interaction(
                session=session,
                receiver_agent="CalculationAgent",
                message_type="response",
                message_content=result,
                execution_time=execution_time,
                success=True
            )
            
            return result
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            error_msg = str(e)
            
            await self.log_interaction(
                session=session,
                receiver_agent="CalculationAgent",
                message_type="error",
                message_content={"error": error_msg},
                execution_time=execution_time,
                success=False,
                error_details=error_msg
            )
            
            logger.error(f"Error in species search: {error_msg}")
            raise
    
    async def search_regional_data(
        self, 
        session: ConversationSession,
        location_name: str
    ) -> Dict:
        """
        Search for regional climate and geographic data
        """
        start_time = datetime.now()
        
        try:
            # Try to find in database
            is_valid, error_msg, region = DataValidationService.validate_location_data(location_name)
            
            if is_valid and region:
                found_data = {
                    "region_id": region.id,
                    "name": region.name,
                    "country": region.country,
                    "latitude": region.latitude,
                    "longitude": region.longitude,
                    "average_temperature": region.average_temperature,
                    "annual_precipitation": region.annual_precipitation,
                    "growing_season_length": region.growing_season_length,
                    "climate_factor": region.climate_factor,
                    "soil_factor": region.soil_factor
                }
                
                result = {
                    "found_data": found_data,
                    "data_sources": ["Internal Database"],
                    "confidence_level": 0.9,
                    "search_status": "success",
                    "recommendations": []
                }
            else:
                # Use agent to search for regional data
                prompt = f"""
                Search for climate and geographic data for location: "{location_name}"
                
                Please provide typical climate parameters for this region including:
                1. Average temperature
                2. Annual precipitation
                3. Growing season length
                4. Climate adjustment factors for forest growth
                
                Provide structured response with available data.
                """
                
                message = TextMessage(content=prompt, source="user")
                response = await self.agent.run(task=message)
                
                if hasattr(response, 'content'):
                    response_content = response.content
                else:
                    response_content = str(response)
                
                try:
                    result = json.loads(response_content)
                except json.JSONDecodeError:
                    result = {
                        "found_data": {},
                        "data_sources": ["Default Values"],
                        "confidence_level": 0.3,
                        "search_status": "partial",
                        "recommendations": [
                            "Use default climate factor of 1.0",
                            "Use default soil factor of 1.0"
                        ]
                    }
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            await self.log_interaction(
                session=session,
                receiver_agent="CalculationAgent",
                message_type="response",
                message_content=result,
                execution_time=execution_time,
                success=True
            )
            
            return result
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            error_msg = str(e)
            
            await self.log_interaction(
                session=session,
                receiver_agent="CalculationAgent",
                message_type="error",
                message_content={"error": error_msg},
                execution_time=execution_time,
                success=False,
                error_details=error_msg
            )
            
            logger.error(f"Error in regional search: {error_msg}")
            raise


class CalculationAgent(BaseForestAgent):
    """
    Agent responsible for performing carbon sequestration calculations
    """

    def __init__(self):
        system_message = """
        You are a Calculation Agent specialized in forest carbon sequestration calculations.
        Your role is to:
        1. Perform carbon sequestration calculations using provided forest data
        2. Validate calculation parameters and results
        3. Provide detailed calculation breakdowns and explanations
        4. Identify potential issues or uncertainties in calculations

        Always respond with structured JSON containing:
        - calculation_results: Dictionary with all calculated values
        - calculation_method: String describing the method used
        - confidence_level: Float between 0-1 indicating result reliability
        - validation_status: String indicating if results are valid
        - explanation: String explaining the calculation process
        - recommendations: List of recommendations for improving accuracy
        """
        super().__init__("CalculationAgent", system_message)

    async def perform_calculation(
        self,
        session: ConversationSession,
        forest_data: ForestData
    ) -> Dict:
        """
        Perform carbon sequestration calculation
        """
        start_time = datetime.now()

        try:
            # Perform the actual calculation using the service
            calc_result = await CarbonCalculationService.calculate_carbon_sequestration(forest_data)

            if calc_result.status == 'completed':
                result = {
                    "calculation_results": {
                        "total_biomass": calc_result.total_biomass,
                        "above_ground_biomass": calc_result.above_ground_biomass,
                        "below_ground_biomass": calc_result.below_ground_biomass,
                        "total_carbon": calc_result.total_carbon,
                        "annual_sequestration": calc_result.annual_sequestration
                    },
                    "calculation_method": calc_result.calculation_method,
                    "confidence_level": 0.8,
                    "validation_status": "valid",
                    "explanation": self._generate_explanation(calc_result),
                    "recommendations": self._generate_recommendations(calc_result),
                    "calculation_id": str(calc_result.calculation_id)
                }
            else:
                result = {
                    "calculation_results": {},
                    "calculation_method": "failed",
                    "confidence_level": 0.0,
                    "validation_status": "invalid",
                    "explanation": f"Calculation failed: {calc_result.error_message}",
                    "recommendations": ["Please check input parameters and try again"],
                    "calculation_id": str(calc_result.calculation_id)
                }

            execution_time = (datetime.now() - start_time).total_seconds()

            await self.log_interaction(
                session=session,
                receiver_agent="OrchestratorAgent",
                message_type="response",
                message_content=result,
                execution_time=execution_time,
                success=(calc_result.status == 'completed')
            )

            return result

        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            error_msg = str(e)

            await self.log_interaction(
                session=session,
                receiver_agent="OrchestratorAgent",
                message_type="error",
                message_content={"error": error_msg},
                execution_time=execution_time,
                success=False,
                error_details=error_msg
            )

            logger.error(f"Error in calculation: {error_msg}")
            raise

    def _generate_explanation(self, calc_result) -> str:
        """Generate human-readable explanation of the calculation"""
        if calc_result.calculation_details:
            params = calc_result.calculation_details.get('parameters_used', {})
            intermediate = calc_result.calculation_details.get('intermediate_calculations', {})

            explanation = f"""
            Carbon sequestration calculation completed using allometric equations.

            Forest Parameters:
            - Area: {params.get('area_hectares', 'N/A')} hectares
            - Tree density: {params.get('tree_density', 'N/A')} trees/hectare
            - Average DBH: {params.get('average_dbh', 'N/A')} cm
            - Average height: {params.get('average_height', 'N/A')} m

            Results:
            - Total biomass: {calc_result.total_biomass:.2f} tons
            - Carbon content: {calc_result.total_carbon:.2f} tons
            - Annual CO2 sequestration: {calc_result.annual_sequestration:.2f} tons/year
            """
            return explanation.strip()

        return "Calculation completed using standard allometric equations."

    def _generate_recommendations(self, calc_result) -> List[str]:
        """Generate recommendations based on calculation results"""
        recommendations = []

        if calc_result.calculation_details:
            params = calc_result.calculation_details.get('parameters_used', {})

            # Check for potential issues
            if params.get('area_hectares', 0) < 1:
                recommendations.append("Consider measuring a larger forest area for more representative results")

            if params.get('tree_density', 0) < 100:
                recommendations.append("Tree density seems low - verify measurements")

            if params.get('average_dbh', 0) < 5:
                recommendations.append("Average DBH is quite small - ensure measurements include mature trees")

        if not recommendations:
            recommendations.append("Results appear reasonable based on provided parameters")

        return recommendations


class OrchestratorAgent(BaseForestAgent):
    """
    Main orchestrator agent that coordinates the entire workflow
    """

    def __init__(self):
        system_message = """
        You are the Orchestrator Agent for the forest carbon sequestration system.
        Your role is to:
        1. Coordinate the workflow between all other agents
        2. Manage conversation flow and data collection
        3. Ensure all required data is collected before calculations
        4. Provide user-friendly responses and guidance
        5. Handle errors and edge cases gracefully

        Always respond with structured JSON containing:
        - response_type: String indicating the type of response (question, info, result, error)
        - message: String with the user-facing message
        - next_action: String indicating what should happen next
        - data_status: Dictionary showing what data has been collected
        - completion_status: Float between 0-1 indicating overall progress
        """
        super().__init__("OrchestratorAgent", system_message)

        # Initialize other agents
        self.input_agent = InputExtractionAgent()
        self.search_agent = SearchEngineAgent()
        self.calculation_agent = CalculationAgent()

    async def process_user_message(
        self,
        session: ConversationSession,
        user_message: str
    ) -> Dict:
        """
        Main method to process user messages and coordinate the workflow
        """
        start_time = datetime.now()

        try:
            # Extract data from user message
            extracted_data = await self.input_agent.extract_data_from_message(
                session, user_message, session.current_step
            )

            # Update session with extracted data
            if extracted_data.get('extracted_data'):
                session = ConversationService.update_session_data(
                    session, session.current_step, extracted_data['extracted_data']
                )

            # Determine next action based on current step and extracted data
            response = await self._determine_next_action(session, extracted_data)

            execution_time = (datetime.now() - start_time).total_seconds()

            await self.log_interaction(
                session=session,
                receiver_agent="UserInterface",
                message_type="response",
                message_content=response,
                execution_time=execution_time,
                success=True
            )

            return response

        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            error_msg = str(e)

            await self.log_interaction(
                session=session,
                receiver_agent="UserInterface",
                message_type="error",
                message_content={"error": error_msg},
                execution_time=execution_time,
                success=False,
                error_details=error_msg
            )

            logger.error(f"Error in orchestrator: {error_msg}")

            return {
                "response_type": "error",
                "message": "I encountered an error processing your request. Please try again.",
                "next_action": "retry",
                "data_status": session.collected_data,
                "completion_status": session.completion_percentage / 100.0
            }

    async def _determine_next_action(self, session: ConversationSession, extracted_data: Dict) -> Dict:
        """Determine the next action based on current state"""
        current_step = session.current_step
        collected_data = session.collected_data

        if current_step == 'species':
            return await self._handle_species_step(session, extracted_data)
        elif current_step == 'location':
            return await self._handle_location_step(session, extracted_data)
        elif current_step == 'area':
            return await self._handle_area_step(session, extracted_data)
        elif current_step == 'tree_measurements':
            return await self._handle_measurements_step(session, extracted_data)
        elif current_step == 'forest_characteristics':
            return await self._handle_characteristics_step(session, extracted_data)
        elif current_step == 'confirmation':
            return await self._handle_confirmation_step(session, extracted_data)
        elif current_step == 'calculation':
            return await self._handle_calculation_step(session, extracted_data)
        else:
            return {
                "response_type": "error",
                "message": "I'm not sure what information I need next. Let's start over.",
                "next_action": "restart",
                "data_status": collected_data,
                "completion_status": 0.0
            }

    async def _handle_species_step(self, session: ConversationSession, extracted_data: Dict) -> Dict:
        """Handle species identification step"""
        if extracted_data.get('clarification_needed', True):
            return {
                "response_type": "question",
                "message": extracted_data.get('next_question', "What type of trees are in your forest? Please provide the species name."),
                "next_action": "await_species",
                "data_status": session.collected_data,
                "completion_status": session.completion_percentage / 100.0
            }
        else:
            # Species identified, search for parameters
            species_name = extracted_data['extracted_data'].get('species_name')
            if species_name:
                search_result = await self.search_agent.search_species_parameters(session, species_name)

                return {
                    "response_type": "info",
                    "message": f"Great! I found information about {species_name}. Now, where is your forest located?",
                    "next_action": "await_location",
                    "data_status": session.collected_data,
                    "completion_status": session.completion_percentage / 100.0
                }
            else:
                return {
                    "response_type": "question",
                    "message": "I couldn't identify the tree species. Could you please specify the type of trees in your forest?",
                    "next_action": "await_species",
                    "data_status": session.collected_data,
                    "completion_status": session.completion_percentage / 100.0
                }

    async def _handle_location_step(self, session: ConversationSession, extracted_data: Dict) -> Dict:
        """Handle location identification step"""
        if extracted_data.get('clarification_needed', True):
            return {
                "response_type": "question",
                "message": extracted_data.get('next_question', "Where is your forest located? Please provide the geographic region or coordinates."),
                "next_action": "await_location",
                "data_status": session.collected_data,
                "completion_status": session.completion_percentage / 100.0
            }
        else:
            location_name = extracted_data['extracted_data'].get('location_name')
            if location_name:
                search_result = await self.search_agent.search_regional_data(session, location_name)

                return {
                    "response_type": "info",
                    "message": f"Perfect! I have the location information. Now, what is the total area of your forest?",
                    "next_action": "await_area",
                    "data_status": session.collected_data,
                    "completion_status": session.completion_percentage / 100.0
                }
            else:
                return {
                    "response_type": "question",
                    "message": "I need the location of your forest. Please provide the region, city, or coordinates.",
                    "next_action": "await_location",
                    "data_status": session.collected_data,
                    "completion_status": session.completion_percentage / 100.0
                }

    async def _handle_area_step(self, session: ConversationSession, extracted_data: Dict) -> Dict:
        """Handle forest area step"""
        if extracted_data.get('clarification_needed', True):
            return {
                "response_type": "question",
                "message": extracted_data.get('next_question', "What is the total area of your forest? Please specify in hectares or acres."),
                "next_action": "await_area",
                "data_status": session.collected_data,
                "completion_status": session.completion_percentage / 100.0
            }
        else:
            return {
                "response_type": "info",
                "message": "Great! Now I need some tree measurements. What is the average diameter at breast height (DBH) of the trees?",
                "next_action": "await_measurements",
                "data_status": session.collected_data,
                "completion_status": session.completion_percentage / 100.0
            }

    async def _handle_measurements_step(self, session: ConversationSession, extracted_data: Dict) -> Dict:
        """Handle tree measurements step"""
        if extracted_data.get('clarification_needed', True):
            return {
                "response_type": "question",
                "message": extracted_data.get('next_question', "I need tree measurements: average diameter at breast height (DBH) in cm, average height in meters, and tree density (trees per hectare)."),
                "next_action": "await_measurements",
                "data_status": session.collected_data,
                "completion_status": session.completion_percentage / 100.0
            }
        else:
            return {
                "response_type": "info",
                "message": "Excellent! Finally, what is the approximate age of your forest and how is it managed?",
                "next_action": "await_characteristics",
                "data_status": session.collected_data,
                "completion_status": session.completion_percentage / 100.0
            }

    async def _handle_characteristics_step(self, session: ConversationSession, extracted_data: Dict) -> Dict:
        """Handle forest characteristics step"""
        if extracted_data.get('clarification_needed', True):
            return {
                "response_type": "question",
                "message": extracted_data.get('next_question', "What is the age of your forest and how is it managed (natural, planted, managed, etc.)?"),
                "next_action": "await_characteristics",
                "data_status": session.collected_data,
                "completion_status": session.completion_percentage / 100.0
            }
        else:
            # All data collected, show summary for confirmation
            summary = self._generate_data_summary(session.collected_data)
            return {
                "response_type": "confirmation",
                "message": f"Perfect! I have collected all the information:\n\n{summary}\n\nIs this information correct? Reply 'yes' to proceed with the calculation or 'no' to make corrections.",
                "next_action": "await_confirmation",
                "data_status": session.collected_data,
                "completion_status": session.completion_percentage / 100.0
            }

    async def _handle_confirmation_step(self, session: ConversationSession, extracted_data: Dict) -> Dict:
        """Handle confirmation step"""
        confirmation = extracted_data['extracted_data'].get('confirmation', '').lower()

        if 'yes' in confirmation or 'correct' in confirmation:
            # Proceed with calculation
            return await self._handle_calculation_step(session, extracted_data)
        else:
            return {
                "response_type": "question",
                "message": "What would you like to correct? Please tell me which information needs to be updated.",
                "next_action": "await_correction",
                "data_status": session.collected_data,
                "completion_status": session.completion_percentage / 100.0
            }

    async def _handle_calculation_step(self, session: ConversationSession, extracted_data: Dict) -> Dict:
        """Handle calculation step"""
        try:
            # Create ForestData object and perform calculation
            forest_data = await self._create_forest_data_from_session(session)
            calc_result = await self.calculation_agent.perform_calculation(session, forest_data)

            if calc_result['validation_status'] == 'valid':
                results = calc_result['calculation_results']
                message = f"""
                🌲 Carbon Sequestration Calculation Results 🌲

                Total Forest Biomass: {results['total_biomass']:.2f} tons
                - Above-ground biomass: {results['above_ground_biomass']:.2f} tons
                - Below-ground biomass: {results['below_ground_biomass']:.2f} tons

                Total Carbon Stored: {results['total_carbon']:.2f} tons
                Annual CO2 Sequestration: {results['annual_sequestration']:.2f} tons/year

                {calc_result['explanation']}

                Would you like to perform another calculation or need any clarifications?
                """

                # Mark session as completed
                ConversationService.complete_session(session)

                return {
                    "response_type": "result",
                    "message": message.strip(),
                    "next_action": "completed",
                    "data_status": session.collected_data,
                    "completion_status": 1.0,
                    "calculation_results": results
                }
            else:
                return {
                    "response_type": "error",
                    "message": f"Calculation failed: {calc_result['explanation']}",
                    "next_action": "retry",
                    "data_status": session.collected_data,
                    "completion_status": session.completion_percentage / 100.0
                }

        except Exception as e:
            logger.error(f"Error in calculation step: {str(e)}")
            return {
                "response_type": "error",
                "message": "I encountered an error during calculation. Please check your data and try again.",
                "next_action": "retry",
                "data_status": session.collected_data,
                "completion_status": session.completion_percentage / 100.0
            }

    def _generate_data_summary(self, collected_data: Dict) -> str:
        """Generate a summary of collected data"""
        summary_parts = []

        for step, data in collected_data.items():
            if step == 'species' and 'species_name' in data:
                summary_parts.append(f"🌳 Species: {data['species_name']}")
            elif step == 'location' and 'location_name' in data:
                summary_parts.append(f"📍 Location: {data['location_name']}")
            elif step == 'area' and 'area' in data:
                unit = data.get('unit', 'hectares')
                summary_parts.append(f"📏 Area: {data['area']} {unit}")
            elif step == 'tree_measurements':
                if 'dbh' in data:
                    summary_parts.append(f"📐 Average DBH: {data['dbh']} cm")
                if 'height' in data:
                    summary_parts.append(f"📏 Average Height: {data['height']} m")
                if 'density' in data:
                    summary_parts.append(f"🌲 Tree Density: {data['density']} trees/hectare")
            elif step == 'forest_characteristics':
                if 'age' in data:
                    summary_parts.append(f"⏰ Forest Age: {data['age']} years")
                if 'management' in data:
                    summary_parts.append(f"🔧 Management: {data['management']}")

        return '\n'.join(summary_parts) if summary_parts else "Data collection in progress..."

    async def _create_forest_data_from_session(self, session: ConversationSession) -> ForestData:
        """Create ForestData object from session data"""
        collected_data = session.collected_data

        # Extract species
        species_name = collected_data.get('species', {}).get('species_name')
        _, _, species = DataValidationService.validate_species_data(species_name)

        # Extract location
        location_name = collected_data.get('location', {}).get('location_name')
        _, _, region = DataValidationService.validate_location_data(location_name)

        # Extract measurements
        area_data = collected_data.get('area', {})
        measurements = collected_data.get('tree_measurements', {})
        characteristics = collected_data.get('forest_characteristics', {})

        # Create ForestData object
        forest_data = ForestData.objects.create(
            session=session,
            species=species,
            region=region,
            area=float(area_data.get('area', 0)),
            area_unit=area_data.get('unit', 'hectares'),
            average_dbh=float(measurements.get('dbh', 0)),
            average_height=float(measurements.get('height', 0)),
            tree_density=float(measurements.get('density', 0)),
            forest_age=int(characteristics.get('age', 0)),
            management_type=characteristics.get('management', 'natural')
        )

        return forest_data
