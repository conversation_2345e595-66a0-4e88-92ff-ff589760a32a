from django.contrib import admin
from .models import (
    TreeSpecies, GeographicRegion, ConversationSession,
    ForestData, CarbonCalculationResult, AgentInteraction, UserMessage
)


@admin.register(TreeSpecies)
class TreeSpeciesAdmin(admin.ModelAdmin):
    list_display = ['name', 'scientific_name', 'wood_density', 'carbon_content', 'created_at']
    list_filter = ['created_at', 'wood_density']
    search_fields = ['name', 'scientific_name']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(GeographicRegion)
class GeographicRegionAdmin(admin.ModelAdmin):
    list_display = ['name', 'country', 'latitude', 'longitude', 'climate_factor', 'created_at']
    list_filter = ['country', 'created_at']
    search_fields = ['name', 'country']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(ConversationSession)
class ConversationSessionAdmin(admin.ModelAdmin):
    list_display = ['session_id', 'user', 'status', 'current_step', 'completion_percentage', 'created_at']
    list_filter = ['status', 'current_step', 'created_at']
    search_fields = ['session_id', 'user__username']
    readonly_fields = ['session_id', 'created_at', 'updated_at']


@admin.register(ForestData)
class ForestDataAdmin(admin.ModelAdmin):
    list_display = ['session', 'species', 'region', 'area', 'area_unit', 'average_dbh', 'created_at']
    list_filter = ['species', 'region', 'area_unit', 'created_at']
    search_fields = ['species__name', 'region__name']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(CarbonCalculationResult)
class CarbonCalculationResultAdmin(admin.ModelAdmin):
    list_display = ['calculation_id', 'status', 'total_carbon', 'annual_sequestration', 'created_at']
    list_filter = ['status', 'calculation_method', 'created_at']
    search_fields = ['calculation_id', 'forest_data__species__name']
    readonly_fields = ['calculation_id', 'created_at', 'updated_at']


@admin.register(AgentInteraction)
class AgentInteractionAdmin(admin.ModelAdmin):
    list_display = ['sender_agent', 'receiver_agent', 'message_type', 'success', 'execution_time', 'created_at']
    list_filter = ['sender_agent', 'receiver_agent', 'message_type', 'success', 'created_at']
    search_fields = ['interaction_id', 'session__session_id']
    readonly_fields = ['interaction_id', 'created_at']


@admin.register(UserMessage)
class UserMessageAdmin(admin.ModelAdmin):
    list_display = ['session', 'message_type', 'agent_name', 'content_preview', 'created_at']
    list_filter = ['message_type', 'agent_name', 'created_at']
    search_fields = ['session__session_id', 'content']
    readonly_fields = ['created_at']

    def content_preview(self, obj):
        return obj.content[:100] + "..." if len(obj.content) > 100 else obj.content
    content_preview.short_description = "Content Preview"
