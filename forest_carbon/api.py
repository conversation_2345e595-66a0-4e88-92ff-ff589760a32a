"""
Forest Carbon Sequestration API

This module provides API endpoints for the conversational interface
to collect forest data and perform carbon sequestration calculations.
"""

import asyncio
import logging
from typing import Dict, Optional
from uuid import UUID

from ninja_extra import api_controller, route
from ninja import Schema
from django.http import StreamingHttpResponse, JsonResponse
from django.shortcuts import get_object_or_404
from django.contrib.auth.models import User

from .models import ConversationSession, CarbonCalculationResult, UserMessage
from .agents import OrchestratorAgent
from .services import ConversationService

logger = logging.getLogger(__name__)


# Request/Response Schemas
class StartConversationRequest(Schema):
    user_id: Optional[int] = None


class StartConversationResponse(Schema):
    session_id: str
    message: str
    status: str


class SendMessageRequest(Schema):
    session_id: str
    message: str


class SendMessageResponse(Schema):
    response_type: str
    message: str
    next_action: str
    data_status: Dict
    completion_status: float
    calculation_results: Optional[Dict] = None


class SessionStatusResponse(Schema):
    session_id: str
    status: str
    current_step: str
    completion_percentage: float
    collected_data: Dict
    steps_remaining: list


class CalculationResultResponse(Schema):
    calculation_id: str
    status: str
    total_biomass: Optional[float] = None
    total_carbon: Optional[float] = None
    annual_sequestration: Optional[float] = None
    calculation_details: Optional[Dict] = None
    error_message: Optional[str] = None


@api_controller("/forest-carbon")
class ForestCarbonController:
    """
    API Controller for forest carbon sequestration conversational interface
    """
    
    def __init__(self):
        self.orchestrator = OrchestratorAgent()
    
    @route.post("/start-conversation", response=StartConversationResponse)
    async def start_conversation(self, request, payload: StartConversationRequest):
        """
        Start a new conversation session for forest carbon calculation.
        
        Returns:
            Session information and initial greeting message
        """
        try:
            # Get user if provided
            user = None
            if payload.user_id:
                try:
                    user = User.objects.get(id=payload.user_id)
                except User.DoesNotExist:
                    pass
            
            # Create new conversation session
            session = ConversationService.create_session(user=user)
            
            # Create initial user message
            UserMessage.objects.create(
                session=session,
                message_type='system',
                content='Conversation started',
                agent_name='System'
            )
            
            initial_message = """
            🌲 Welcome to the Forest Carbon Sequestration Calculator! 🌲
            
            I'll help you calculate how much carbon your forest sequesters annually. 
            To get started, I need to collect some information about your forest.
            
            First, what type of trees are in your forest? Please provide the species name 
            (e.g., "Oak", "Pine", "Eucalyptus", etc.).
            """
            
            return StartConversationResponse(
                session_id=str(session.session_id),
                message=initial_message.strip(),
                status="active"
            )
            
        except Exception as e:
            logger.error(f"Error starting conversation: {str(e)}")
            return JsonResponse(
                {"error": "Failed to start conversation. Please try again."},
                status=500
            )
    
    @route.post("/send-message", response=SendMessageResponse)
    async def send_message(self, request, payload: SendMessageRequest):
        """
        Send a message in an existing conversation session.
        
        Args:
            payload: Message data including session_id and message content
            
        Returns:
            Agent response with next steps
        """
        try:
            # Get session
            session = get_object_or_404(
                ConversationSession, 
                session_id=payload.session_id,
                status__in=['active', 'completed']
            )
            
            # Log user message
            UserMessage.objects.create(
                session=session,
                message_type='user',
                content=payload.message
            )
            
            # Process message with orchestrator agent
            response = await self.orchestrator.process_user_message(session, payload.message)
            
            # Log agent response
            UserMessage.objects.create(
                session=session,
                message_type='agent',
                content=response['message'],
                agent_name='OrchestratorAgent'
            )
            
            return SendMessageResponse(**response)
            
        except ConversationSession.DoesNotExist:
            return JsonResponse(
                {"error": "Session not found or expired"},
                status=404
            )
        except Exception as e:
            logger.error(f"Error processing message: {str(e)}")
            return JsonResponse(
                {"error": "Failed to process message. Please try again."},
                status=500
            )
    
    @route.get("/session/{session_id}/status", response=SessionStatusResponse)
    async def get_session_status(self, request, session_id: str):
        """
        Get the current status of a conversation session.
        
        Args:
            session_id: UUID of the conversation session
            
        Returns:
            Current session status and progress information
        """
        try:
            session = get_object_or_404(ConversationSession, session_id=session_id)
            progress = ConversationService.get_session_progress(session)
            
            return SessionStatusResponse(
                session_id=progress['session_id'],
                status=progress['status'],
                current_step=progress['current_step'],
                completion_percentage=progress['completion_percentage'],
                collected_data=progress['collected_data'],
                steps_remaining=progress['steps_remaining']
            )
            
        except ConversationSession.DoesNotExist:
            return JsonResponse(
                {"error": "Session not found"},
                status=404
            )
        except Exception as e:
            logger.error(f"Error getting session status: {str(e)}")
            return JsonResponse(
                {"error": "Failed to get session status"},
                status=500
            )
    
    @route.get("/session/{session_id}/messages")
    async def get_session_messages(self, request, session_id: str):
        """
        Get all messages from a conversation session.
        
        Args:
            session_id: UUID of the conversation session
            
        Returns:
            List of all messages in the session
        """
        try:
            session = get_object_or_404(ConversationSession, session_id=session_id)
            messages = UserMessage.objects.filter(session=session).order_by('created_at')
            
            message_list = []
            for msg in messages:
                message_list.append({
                    "message_type": msg.message_type,
                    "content": msg.content,
                    "agent_name": msg.agent_name,
                    "created_at": msg.created_at.isoformat(),
                    "extracted_data": msg.extracted_data
                })
            
            return {
                "session_id": session_id,
                "messages": message_list,
                "total_messages": len(message_list)
            }
            
        except ConversationSession.DoesNotExist:
            return JsonResponse(
                {"error": "Session not found"},
                status=404
            )
        except Exception as e:
            logger.error(f"Error getting session messages: {str(e)}")
            return JsonResponse(
                {"error": "Failed to get session messages"},
                status=500
            )
    
    @route.get("/calculation/{calculation_id}", response=CalculationResultResponse)
    async def get_calculation_result(self, request, calculation_id: str):
        """
        Get the result of a carbon sequestration calculation.
        
        Args:
            calculation_id: UUID of the calculation
            
        Returns:
            Calculation results and details
        """
        try:
            calc_result = get_object_or_404(CarbonCalculationResult, calculation_id=calculation_id)
            
            return CalculationResultResponse(
                calculation_id=str(calc_result.calculation_id),
                status=calc_result.status,
                total_biomass=calc_result.total_biomass,
                total_carbon=calc_result.total_carbon,
                annual_sequestration=calc_result.annual_sequestration,
                calculation_details=calc_result.calculation_details,
                error_message=calc_result.error_message
            )
            
        except CarbonCalculationResult.DoesNotExist:
            return JsonResponse(
                {"error": "Calculation not found"},
                status=404
            )
        except Exception as e:
            logger.error(f"Error getting calculation result: {str(e)}")
            return JsonResponse(
                {"error": "Failed to get calculation result"},
                status=500
            )
    
    @route.post("/session/{session_id}/restart")
    async def restart_session(self, request, session_id: str):
        """
        Restart a conversation session, clearing collected data.
        
        Args:
            session_id: UUID of the conversation session
            
        Returns:
            Confirmation of restart
        """
        try:
            session = get_object_or_404(ConversationSession, session_id=session_id)
            
            # Reset session data
            session.collected_data = {}
            session.current_step = 'species'
            session.completion_percentage = 0.0
            session.status = 'active'
            session.save()
            
            # Log restart message
            UserMessage.objects.create(
                session=session,
                message_type='system',
                content='Session restarted',
                agent_name='System'
            )
            
            return {
                "message": "Session restarted successfully. Let's start over with collecting your forest information.",
                "session_id": session_id,
                "status": "active"
            }
            
        except ConversationSession.DoesNotExist:
            return JsonResponse(
                {"error": "Session not found"},
                status=404
            )
        except Exception as e:
            logger.error(f"Error restarting session: {str(e)}")
            return JsonResponse(
                {"error": "Failed to restart session"},
                status=500
            )
    
    @route.get("/sessions")
    async def list_sessions(self, request, user_id: Optional[int] = None, status: Optional[str] = None):
        """
        List conversation sessions with optional filtering.
        
        Args:
            user_id: Filter by user ID (optional)
            status: Filter by session status (optional)
            
        Returns:
            List of sessions matching the criteria
        """
        try:
            sessions = ConversationSession.objects.all()
            
            if user_id:
                sessions = sessions.filter(user_id=user_id)
            
            if status:
                sessions = sessions.filter(status=status)
            
            sessions = sessions.order_by('-created_at')[:50]  # Limit to 50 most recent
            
            session_list = []
            for session in sessions:
                session_list.append({
                    "session_id": str(session.session_id),
                    "user_id": session.user_id,
                    "status": session.status,
                    "current_step": session.current_step,
                    "completion_percentage": session.completion_percentage,
                    "created_at": session.created_at.isoformat(),
                    "updated_at": session.updated_at.isoformat()
                })
            
            return {
                "sessions": session_list,
                "total_sessions": len(session_list)
            }
            
        except Exception as e:
            logger.error(f"Error listing sessions: {str(e)}")
            return JsonResponse(
                {"error": "Failed to list sessions"},
                status=500
            )
