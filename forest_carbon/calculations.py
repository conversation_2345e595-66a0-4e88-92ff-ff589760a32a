"""
Forest Carbon Sequestration Calculation Module

This module contains mathematical functions for calculating forest carbon sequestration
using allometric equations, biomass calculations, and species-specific parameters.
"""

import math
from typing import Dict, Tu<PERSON>, Optional
from dataclasses import dataclass


@dataclass
class ForestParameters:
    """Data class to hold forest parameters for calculations"""
    area_hectares: float
    tree_density: float  # trees per hectare
    average_dbh: float  # diameter at breast height in cm
    average_height: float  # height in meters
    forest_age: int  # age in years
    
    # Species-specific parameters
    allometric_a: float = 0.0673
    allometric_b: float = 2.085
    allometric_c: float = 0.756
    allometric_d: float = 0.0
    wood_density: float = 0.5  # g/cm³
    carbon_content: float = 0.47  # 47%
    annual_growth_rate: float = 0.02  # 2%
    
    # Regional factors
    climate_factor: float = 1.0
    soil_factor: float = 1.0


@dataclass
class CarbonCalculationResult:
    """Data class to hold calculation results"""
    total_biomass: float  # tons
    above_ground_biomass: float  # tons
    below_ground_biomass: float  # tons
    total_carbon: float  # tons
    annual_sequestration: float  # tons CO2/year
    calculation_details: Dict


class CarbonCalculator:
    """
    Main calculator class for forest carbon sequestration calculations
    """
    
    # Constants
    CO2_TO_C_RATIO = 44.01 / 12.01  # Molecular weight ratio CO2/C
    ROOT_TO_SHOOT_RATIO = 0.25  # Typical root biomass as fraction of above-ground biomass
    
    @staticmethod
    def calculate_individual_tree_biomass(
        dbh: float, 
        height: float, 
        wood_density: float,
        allometric_a: float = 0.0673,
        allometric_b: float = 2.085,
        allometric_c: float = 0.756,
        allometric_d: float = 0.0
    ) -> float:
        """
        Calculate biomass for an individual tree using allometric equations.
        
        General form: Biomass = a * (DBH^b) * (Height^c) * (Wood_density^d)
        
        Args:
            dbh: Diameter at breast height in cm
            height: Tree height in meters
            wood_density: Wood density in g/cm³
            allometric_a, b, c, d: Allometric equation parameters
            
        Returns:
            Tree biomass in kg
        """
        if dbh <= 0 or height <= 0:
            return 0.0
            
        # Convert DBH from cm to appropriate units for the equation
        dbh_m = dbh / 100.0  # Convert to meters
        
        # Calculate biomass using allometric equation
        if allometric_d > 0:
            biomass = allometric_a * (dbh_m ** allometric_b) * (height ** allometric_c) * (wood_density ** allometric_d)
        else:
            # Simplified equation without wood density
            biomass = allometric_a * (dbh_m ** allometric_b) * (height ** allometric_c)
        
        return max(0.0, biomass)  # Ensure non-negative result
    
    @staticmethod
    def calculate_above_ground_biomass(
        area_hectares: float,
        tree_density: float,
        average_dbh: float,
        average_height: float,
        wood_density: float,
        allometric_a: float = 0.0673,
        allometric_b: float = 2.085,
        allometric_c: float = 0.756,
        allometric_d: float = 0.0
    ) -> float:
        """
        Calculate total above-ground biomass for the forest area.
        
        Args:
            area_hectares: Forest area in hectares
            tree_density: Number of trees per hectare
            average_dbh: Average diameter at breast height in cm
            average_height: Average tree height in meters
            wood_density: Wood density in g/cm³
            allometric_a, b, c, d: Allometric equation parameters
            
        Returns:
            Total above-ground biomass in tons
        """
        # Calculate biomass per tree in kg
        biomass_per_tree = CarbonCalculator.calculate_individual_tree_biomass(
            average_dbh, average_height, wood_density,
            allometric_a, allometric_b, allometric_c, allometric_d
        )
        
        # Calculate total number of trees
        total_trees = area_hectares * tree_density
        
        # Calculate total biomass in kg, then convert to tons
        total_biomass_kg = total_trees * biomass_per_tree
        total_biomass_tons = total_biomass_kg / 1000.0
        
        return total_biomass_tons
    
    @staticmethod
    def calculate_below_ground_biomass(above_ground_biomass: float, root_to_shoot_ratio: float = None) -> float:
        """
        Calculate below-ground (root) biomass based on above-ground biomass.
        
        Args:
            above_ground_biomass: Above-ground biomass in tons
            root_to_shoot_ratio: Ratio of root biomass to above-ground biomass
            
        Returns:
            Below-ground biomass in tons
        """
        if root_to_shoot_ratio is None:
            root_to_shoot_ratio = CarbonCalculator.ROOT_TO_SHOOT_RATIO
            
        return above_ground_biomass * root_to_shoot_ratio
    
    @staticmethod
    def calculate_carbon_content(total_biomass: float, carbon_content_ratio: float = 0.47) -> float:
        """
        Calculate carbon content from total biomass.
        
        Args:
            total_biomass: Total biomass in tons
            carbon_content_ratio: Carbon content as a fraction (default 47%)
            
        Returns:
            Carbon content in tons
        """
        return total_biomass * carbon_content_ratio
    
    @staticmethod
    def calculate_annual_sequestration(
        current_carbon: float,
        annual_growth_rate: float,
        climate_factor: float = 1.0,
        soil_factor: float = 1.0
    ) -> float:
        """
        Calculate annual carbon sequestration rate.
        
        Args:
            current_carbon: Current carbon stock in tons
            annual_growth_rate: Annual growth rate as a fraction
            climate_factor: Climate adjustment factor
            soil_factor: Soil quality adjustment factor
            
        Returns:
            Annual carbon sequestration in tons CO2/year
        """
        # Calculate annual carbon increase
        annual_carbon_increase = current_carbon * annual_growth_rate * climate_factor * soil_factor
        
        # Convert carbon to CO2 equivalent
        annual_co2_sequestration = annual_carbon_increase * CarbonCalculator.CO2_TO_C_RATIO
        
        return annual_co2_sequestration
    
    @classmethod
    def calculate_forest_carbon_sequestration(cls, params: ForestParameters) -> CarbonCalculationResult:
        """
        Main calculation method that performs complete carbon sequestration calculation.
        
        Args:
            params: ForestParameters object containing all necessary parameters
            
        Returns:
            CarbonCalculationResult object with all calculation results
        """
        # Calculate above-ground biomass
        above_ground_biomass = cls.calculate_above_ground_biomass(
            params.area_hectares,
            params.tree_density,
            params.average_dbh,
            params.average_height,
            params.wood_density,
            params.allometric_a,
            params.allometric_b,
            params.allometric_c,
            params.allometric_d
        )
        
        # Calculate below-ground biomass
        below_ground_biomass = cls.calculate_below_ground_biomass(above_ground_biomass)
        
        # Calculate total biomass
        total_biomass = above_ground_biomass + below_ground_biomass
        
        # Calculate carbon content
        total_carbon = cls.calculate_carbon_content(total_biomass, params.carbon_content)
        
        # Calculate annual sequestration
        annual_sequestration = cls.calculate_annual_sequestration(
            total_carbon,
            params.annual_growth_rate,
            params.climate_factor,
            params.soil_factor
        )
        
        # Prepare calculation details
        calculation_details = {
            "method": "allometric_equation",
            "parameters_used": {
                "area_hectares": params.area_hectares,
                "tree_density": params.tree_density,
                "average_dbh": params.average_dbh,
                "average_height": params.average_height,
                "wood_density": params.wood_density,
                "carbon_content": params.carbon_content,
                "annual_growth_rate": params.annual_growth_rate,
                "climate_factor": params.climate_factor,
                "soil_factor": params.soil_factor,
                "allometric_parameters": {
                    "a": params.allometric_a,
                    "b": params.allometric_b,
                    "c": params.allometric_c,
                    "d": params.allometric_d
                }
            },
            "intermediate_calculations": {
                "total_trees": params.area_hectares * params.tree_density,
                "biomass_per_tree_kg": cls.calculate_individual_tree_biomass(
                    params.average_dbh, params.average_height, params.wood_density,
                    params.allometric_a, params.allometric_b, params.allometric_c, params.allometric_d
                ),
                "root_to_shoot_ratio": cls.ROOT_TO_SHOOT_RATIO,
                "co2_to_carbon_ratio": cls.CO2_TO_C_RATIO
            }
        }
        
        return CarbonCalculationResult(
            total_biomass=total_biomass,
            above_ground_biomass=above_ground_biomass,
            below_ground_biomass=below_ground_biomass,
            total_carbon=total_carbon,
            annual_sequestration=annual_sequestration,
            calculation_details=calculation_details
        )


def validate_forest_parameters(params: ForestParameters) -> Tuple[bool, Optional[str]]:
    """
    Validate forest parameters for calculation.
    
    Args:
        params: ForestParameters object to validate
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    if params.area_hectares <= 0:
        return False, "Forest area must be greater than 0"
    
    if params.tree_density <= 0:
        return False, "Tree density must be greater than 0"
    
    if params.average_dbh <= 0:
        return False, "Average DBH must be greater than 0"
    
    if params.average_height <= 0:
        return False, "Average height must be greater than 0"
    
    if params.wood_density <= 0:
        return False, "Wood density must be greater than 0"
    
    if not (0 < params.carbon_content <= 1):
        return False, "Carbon content must be between 0 and 1"
    
    if params.annual_growth_rate < 0:
        return False, "Annual growth rate cannot be negative"
    
    return True, None
