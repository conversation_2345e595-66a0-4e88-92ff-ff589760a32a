from ninja import Schema
from typing import List, Optional

class DetectionResult(Schema):
    """检测结果数据结构"""
    class_name: str
    confidence: float
    bbox: List[float]  # [x1, y1, x2, y2]

class YOLOPredictRequest(Schema):
    """YOLO预测请求数据结构"""
    image_base64: str
    model_name: str
    confidence: float = 0.5

class YOLOPredictResponse(Schema):
    """YOLO预测响应数据结构"""
    success: bool
    model_name: Optional[str] = None
    confidence_threshold: Optional[float] = None
    detection_count: Optional[int] = None
    detections: Optional[List[DetectionResult]] = None
    processed_image: Optional[str] = None
    error: Optional[str] = None

class ModelInfo(Schema):
    """模型信息数据结构"""
    name: str
    description: str
    classes: List[str]

class ModelsResponse(Schema):
    """模型列表响应数据结构"""
    success: bool
    models: Optional[dict] = None
    error: Optional[str] = None

class HealthResponse(Schema):
    """健康检查响应数据结构"""
    success: bool
    status: str
    message: Optional[str] = None
    error: Optional[str] = None 