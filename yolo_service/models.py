from django.db import models
import torch
from ultralytics import YOLO
import os
from pathlib import Path
import threading
import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import io
import base64

class YOLOModelManager:
    """YOLO模型管理器，负责加载和管理所有YOLO模型"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(YOLOModelManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, 'initialized'):
            self.models = {}
            self.model_paths = {
                'fire_smoke': 'yolo/fire_smoke.pt',
                'forestry_bug': 'yolo/forestry_bug.pt'
            }
            self.class_names = {
                'fire_smoke': ['fire', 'smoke'],
                'forestry_bug': ['bug', 'damage']
            }
            self.initialized = True
    
    def load_model(self, model_name):
        """加载指定的YOLO模型"""
        if model_name not in self.models:
            if model_name not in self.model_paths:
                raise ValueError(f"未知的模型名称: {model_name}")
            
            model_path = self.model_paths[model_name]
            if not os.path.exists(model_path):
                raise FileNotFoundError(f"模型文件不存在: {model_path}")
            
            # 加载YOLO模型
            self.models[model_name] = YOLO(model_path)
            print(f"已加载模型: {model_name}")
        
        return self.models[model_name]
    
    def get_model(self, model_name):
        """获取已加载的模型"""
        if model_name not in self.models:
            return self.load_model(model_name)
        return self.models[model_name]
    
    def predict(self, model_name, image_path_or_array, confidence=0.5):
        """使用指定模型进行预测"""
        model = self.get_model(model_name)
        
        # 进行预测
        results = model(image_path_or_array, conf=confidence)
        
        return results
    
    def draw_predictions(self, image, results, model_name):
        """在图像上绘制预测结果"""
        if isinstance(image, str):
            # 如果是文件路径，读取图像
            image = cv2.imread(image)
            if image is None:
                raise ValueError(f"无法读取图像文件: {image}")
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        elif isinstance(image, np.ndarray):
            # 如果是numpy数组，确保是RGB格式
            if len(image.shape) == 3 and image.shape[2] == 3:
                if image.dtype == np.uint8:
                    pass  # 已经是正确格式
                else:
                    image = (image * 255).astype(np.uint8)
        
        # 转换为PIL图像以便绘制
        pil_image = Image.fromarray(image)
        draw = ImageDraw.Draw(pil_image)
        
        # 获取类别名称
        class_names = self.class_names.get(model_name, [])
        
        # 定义颜色
        colors = [
            (255, 0, 0),    # 红色
            (0, 255, 0),    # 绿色
            (0, 0, 255),    # 蓝色
            (255, 255, 0),  # 黄色
            (255, 0, 255),  # 紫色
            (0, 255, 255),  # 青色
        ]
        
        # 尝试加载字体
        try:
            font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", 20)
        except:
            font = ImageFont.load_default()
        
        detection_count = 0
        
        # 遍历检测结果
        for result in results:
            boxes = result.boxes
            if boxes is not None:
                for box in boxes:
                    # 获取边界框坐标
                    x1, y1, x2, y2 = box.xyxy[0].tolist()
                    confidence = box.conf[0].item()
                    class_id = int(box.cls[0].item())
                    
                    # 获取类别名称
                    if class_id < len(class_names):
                        class_name = class_names[class_id]
                    else:
                        class_name = f"class_{class_id}"
                    
                    # 选择颜色
                    color = colors[class_id % len(colors)]
                    
                    # 绘制边界框
                    draw.rectangle([x1, y1, x2, y2], outline=color, width=3)
                    
                    # 绘制标签
                    label = f"{class_name}: {confidence:.2f}"
                    
                    # 计算文本尺寸
                    bbox = draw.textbbox((0, 0), label, font=font)
                    text_width = bbox[2] - bbox[0]
                    text_height = bbox[3] - bbox[1]
                    
                    # 绘制文本背景
                    draw.rectangle(
                        [x1, y1 - text_height - 5, x1 + text_width + 10, y1],
                        fill=color
                    )
                    
                    # 绘制文本
                    draw.text((x1 + 5, y1 - text_height - 2), label, fill=(255, 255, 255), font=font)
                    
                    detection_count += 1
        
        return pil_image, detection_count
    
    def process_image(self, image_data, model_name, confidence=0.5):
        """处理图像数据，返回处理后的图像"""
        # 如果是base64编码，先解码
        if isinstance(image_data, str):
            try:
                image_data = base64.b64decode(image_data)
            except:
                pass
        
        # 将图像数据转换为PIL图像
        if isinstance(image_data, bytes):
            image = Image.open(io.BytesIO(image_data))
        elif isinstance(image_data, str):
            # 如果是字符串，尝试作为文件路径读取
            image = Image.open(image_data)
        else:
            image = image_data
        
        # 转换为RGB格式
        if hasattr(image, 'mode') and image.mode != 'RGB':
            image = image.convert('RGB')
        
        # 转换为numpy数组
        image_array = np.array(image)
        
        # 进行预测
        results = self.predict(model_name, image_array, confidence)
        
        # 绘制预测结果
        processed_image, detection_count = self.draw_predictions(image_array, results, model_name)
        
        return processed_image, detection_count, results
