from ninja_extra import api_controller, route
from ninja import File, Form, UploadedFile
from django.http import JsonResponse, HttpResponse
from typing import Optional, List
import base64
import io
import json
from PIL import Image
import logging

from .models import YOLOModelManager
from .schemas import YOLOPredictRequest, YOLOPredictResponse, DetectionResult

logger = logging.getLogger(__name__)

@api_controller("/yolo", tags=["图像检测"])
class YOLOController:
    """YOLO图像检测控制器"""
    
    def __init__(self):
        self.model_manager = YOLOModelManager()
    
    @route.post("/predict", response=YOLOPredictResponse)
    def predict_image(self, request, image: UploadedFile, model_name: str, confidence: float = 0.5):
        """
        图像预测接口
        
        参数:
        - image: 上传的图像文件
        - model_name: 模型名称 (fire_smoke 或 forestry_bug)
        - confidence: 置信度阈值 (0.0-1.0)
        
        返回:
        - 处理后的图像 (base64编码)
        - 检测结果统计
        """
        try:
            # 验证模型名称
            if model_name not in ['fire_smoke', 'forestry_bug']:
                return JsonResponse({
                    "success": False,
                    "error": "模型名称必须是 'fire_smoke' 或 'forestry_bug'"
                }, status=400)
            
            # 验证置信度
            if not 0.0 <= confidence <= 1.0:
                return JsonResponse({
                    "success": False,
                    "error": "置信度必须在 0.0 到 1.0 之间"
                }, status=400)
            
            # 读取图像数据
            image_data = image.read()
            
            # 处理图像
            processed_image, detection_count, results = self.model_manager.process_image(
                image_data, model_name, confidence
            )
            
            # 将处理后的图像转换为base64
            output_buffer = io.BytesIO()
            processed_image.save(output_buffer, format='JPEG', quality=95)
            processed_image_base64 = base64.b64encode(output_buffer.getvalue()).decode('utf-8')
            
            # 提取检测结果详情
            detections = []
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        x1, y1, x2, y2 = box.xyxy[0].tolist()
                        conf = box.conf[0].item()
                        class_id = int(box.cls[0].item())
                        
                        # 获取类别名称
                        class_names = self.model_manager.class_names.get(model_name, [])
                        if class_id < len(class_names):
                            class_name = class_names[class_id]
                        else:
                            class_name = f"class_{class_id}"
                        
                        detections.append({
                            "class_name": class_name,
                            "confidence": conf,
                            "bbox": [x1, y1, x2, y2]
                        })
            
            return {
                "success": True,
                "model_name": model_name,
                "confidence_threshold": confidence,
                "detection_count": detection_count,
                "detections": detections,
                "processed_image": f"data:image/jpeg;base64,{processed_image_base64}"
            }
            
        except Exception as e:
            logger.error(f"图像处理错误: {str(e)}")
            return JsonResponse({
                "success": False,
                "error": f"图像处理失败: {str(e)}"
            }, status=500)
    
    @route.post("/predict_base64", response=YOLOPredictResponse)
    def predict_base64_image(self, request, data: YOLOPredictRequest):
        """
        Base64图像预测接口
        
        参数:
        - image_base64: Base64编码的图像数据
        - model_name: 模型名称 (fire_smoke 或 forestry_bug)
        - confidence: 置信度阈值 (0.0-1.0)
        
        返回:
        - 处理后的图像 (base64编码)
        - 检测结果统计
        """
        try:
            # 验证模型名称
            if data.model_name not in ['fire_smoke', 'forestry_bug']:
                return JsonResponse({
                    "success": False,
                    "error": "模型名称必须是 'fire_smoke' 或 'forestry_bug'"
                }, status=400)
            
            # 验证置信度
            if not 0.0 <= data.confidence <= 1.0:
                return JsonResponse({
                    "success": False,
                    "error": "置信度必须在 0.0 到 1.0 之间"
                }, status=400)
            
            # 处理base64图像数据
            image_data = data.image_base64
            if image_data.startswith('data:image/'):
                image_data = image_data.split(',')[1]
            
            # 处理图像
            processed_image, detection_count, results = self.model_manager.process_image(
                image_data, data.model_name, data.confidence
            )
            
            # 将处理后的图像转换为base64
            output_buffer = io.BytesIO()
            processed_image.save(output_buffer, format='JPEG', quality=95)
            processed_image_base64 = base64.b64encode(output_buffer.getvalue()).decode('utf-8')
            
            # 提取检测结果详情
            detections = []
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        x1, y1, x2, y2 = box.xyxy[0].tolist()
                        conf = box.conf[0].item()
                        class_id = int(box.cls[0].item())
                        
                        # 获取类别名称
                        class_names = self.model_manager.class_names.get(data.model_name, [])
                        if class_id < len(class_names):
                            class_name = class_names[class_id]
                        else:
                            class_name = f"class_{class_id}"
                        
                        detections.append({
                            "class_name": class_name,
                            "confidence": conf,
                            "bbox": [x1, y1, x2, y2]
                        })
            
            return {
                "success": True,
                "model_name": data.model_name,
                "confidence_threshold": data.confidence,
                "detection_count": detection_count,
                "detections": detections,
                "processed_image": f"data:image/jpeg;base64,{processed_image_base64}"
            }
            
        except Exception as e:
            logger.error(f"图像处理错误: {str(e)}")
            return JsonResponse({
                "success": False,
                "error": f"图像处理失败: {str(e)}"
            }, status=500)
    
    @route.get("/models")
    def get_available_models(self):
        """获取可用的模型列表"""
        return {
            "success": True,
            "models": {
                "fire_smoke": {
                    "name": "火灾烟雾检测模型",
                    "description": "用于检测火灾和烟雾的YOLO模型",
                    "classes": ["fire", "smoke"]
                },
                "forestry_bug": {
                    "name": "森林虫害检测模型", 
                    "description": "用于检测森林虫害的YOLO模型",
                    "classes": ["bug", "damage"]
                }
            }
        }
    
    @route.get("/health")
    def health_check(self):
        """健康检查接口"""
        try:
            # 尝试加载模型以检查服务状态
            self.model_manager.get_model('fire_smoke')
            self.model_manager.get_model('forestry_bug')
            
            return {
                "success": True,
                "status": "healthy",
                "message": "YOLO服务运行正常"
            }
        except Exception as e:
            return JsonResponse({
                "success": False,
                "status": "unhealthy",
                "error": str(e)
            }, status=500) 