from django.shortcuts import render
from django.http import HttpResponse
import os
from pathlib import Path

# Create your views here.

def yolo_test_view(request):
    """YOLO测试页面视图"""
    # 读取HTML文件内容
    template_path = Path(__file__).parent / 'templates' / 'yolo_test.html'
    
    if template_path.exists():
        with open(template_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
        return HttpResponse(html_content)
    else:
        return HttpResponse("测试页面不存在", status=404)
