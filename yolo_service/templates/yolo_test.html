<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YOLO图像检测测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="file"], select {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        input[type="range"] {
            width: 100%;
            margin: 10px 0;
        }
        .confidence-display {
            text-align: center;
            font-size: 18px;
            color: #666;
            margin-bottom: 10px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 30px;
            padding: 20px;
            border-radius: 5px;
        }
        .result.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .image-container {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
            gap: 20px;
        }
        .image-box {
            flex: 1;
            text-align: center;
        }
        .image-box img {
            max-width: 100%;
            max-height: 400px;
            border-radius: 5px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-box h3 {
            margin: 10px 0;
            color: #333;
        }
        .detection-info {
            margin-top: 15px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .detection-item {
            margin: 5px 0;
            padding: 8px;
            background-color: white;
            border-radius: 3px;
            border-left: 4px solid #4CAF50;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        .loading::after {
            content: "...";
            animation: dots 1s steps(5, end) infinite;
        }
        @keyframes dots {
            0%, 20% { content: "."; }
            40% { content: ".."; }
            60% { content: "..."; }
            80%, 100% { content: "...."; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 YOLO图像检测测试 🌲</h1>
        
        <form id="uploadForm" enctype="multipart/form-data">
            <div class="form-group">
                <label for="imageFile">选择图像文件:</label>
                <input type="file" id="imageFile" name="image" accept="image/*" required>
            </div>
            
            <div class="form-group">
                <label for="modelName">选择检测模型:</label>
                <select id="modelName" name="model_name" required>
                    <option value="">请选择模型</option>
                    <option value="fire_smoke">火灾烟雾检测</option>
                    <option value="forestry_bug">森林虫害检测</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="confidence">置信度阈值:</label>
                <div class="confidence-display">
                    <span id="confidenceValue">0.5</span>
                </div>
                <input type="range" id="confidence" name="confidence" min="0.1" max="1.0" step="0.1" value="0.5">
            </div>
            
            <button type="submit">开始检测</button>
        </form>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        // 更新置信度显示
        document.getElementById('confidence').addEventListener('input', function() {
            document.getElementById('confidenceValue').textContent = this.value;
        });
        
        // 处理表单提交
        document.getElementById('uploadForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const resultDiv = document.getElementById('result');
            
            // 显示加载状态
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.innerHTML = '<div class="loading">正在处理图像，请稍候</div>';
            
            // 发送请求
            fetch('/api/yolo/predict', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccess(data);
                } else {
                    showError(data.error || '处理失败');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showError('网络错误或服务器错误');
            });
        });
        
        function showSuccess(data) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = 'result success';
            
            let html = `
                <h3>✅ 检测完成!</h3>
                <p><strong>使用模型:</strong> ${getModelName(data.model_name)}</p>
                <p><strong>置信度阈值:</strong> ${data.confidence_threshold}</p>
                <p><strong>检测到目标数量:</strong> ${data.detection_count}</p>
            `;
            
            if (data.detections && data.detections.length > 0) {
                html += '<div class="detection-info"><h4>检测详情:</h4>';
                data.detections.forEach((detection, index) => {
                    html += `
                        <div class="detection-item">
                            <strong>目标 ${index + 1}:</strong> ${detection.class_name} 
                            (置信度: ${(detection.confidence * 100).toFixed(1)}%)
                        </div>
                    `;
                });
                html += '</div>';
            }
            
            if (data.processed_image) {
                html += `
                    <div class="image-container">
                        <div class="image-box">
                            <h3>处理后的图像</h3>
                            <img src="${data.processed_image}" alt="处理后的图像">
                        </div>
                    </div>
                `;
            }
            
            resultDiv.innerHTML = html;
        }
        
        function showError(message) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = 'result error';
            resultDiv.innerHTML = `
                <h3>❌ 处理失败</h3>
                <p>${message}</p>
            `;
        }
        
        function getModelName(modelKey) {
            const modelNames = {
                'fire_smoke': '火灾烟雾检测模型',
                'forestry_bug': '森林虫害检测模型'
            };
            return modelNames[modelKey] || modelKey;
        }
        
        // 页面加载时检查服务状态
        window.addEventListener('load', function() {
            fetch('/api/yolo/health')
                .then(response => response.json())
                .then(data => {
                    if (!data.success) {
                        console.warn('YOLO服务状态异常:', data.error);
                    }
                })
                .catch(error => {
                    console.error('无法连接到YOLO服务:', error);
                });
        });
    </script>
</body>
</html> 