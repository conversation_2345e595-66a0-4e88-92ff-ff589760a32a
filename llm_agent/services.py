from typing import As<PERSON><PERSON>enerator, Dict, List, Tu<PERSON>

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.messages import TextMessage, MultiModalMessage, ModelClientStreamingChunkEvent
from autogen_core import Image
from autogen_ext.models.ollama import OllamaChatCompletionClient
from autogen_core.models import ModelInfo
from rag_qa.ollama_rag_service import ollama_rag_service


class LLMService:
    async def analyze_fire_situation_with_ollama(
        self, image_b64: str
    ) -> AsyncGenerator[str, None]:
        """
        Analyzes a base64 encoded image using an AutoGen agent that connects to a local Ollama model.
        Streams the response.
        """
        # 清理 Base64 字符串，移除可能的 data URI 前缀
        clean_base64 = image_b64
        if image_b64.startswith("data:image"):
            # 提取纯净的 Base64 部分
            clean_base64 = image_b64.split(",", 1)[1]

        model_client = OllamaChatCompletionClient(
            model="qwen2.5vl:32b-fp16",
            model_info={
                "family": "qwen",
                "vision": True,
                "function_calling": False,
                "structured_output": False,
                "json_output": False,
            }
        )

        agent = AssistantAgent(
            name="FireAnalyst",
            model_client=model_client,
            system_message="You are a helpful AI assistant that analyzes images for forest fires or smoke. "
            "Describe what you see clearly. Respond in Chinese.",
            model_client_stream=True,
        )

        message = MultiModalMessage(
            content=[
                "Analyze the attached image for fire and smoke. Respond in Chinese.",
                Image.from_base64(clean_base64),
            ],
            source="user",
        )

        try:
            async for msg in agent.run_stream(task=message):
                if isinstance(msg, ModelClientStreamingChunkEvent):
                    yield msg.content
                elif isinstance(msg, TextMessage):
                    # 跳过最终的完整消息，避免重复输出
                    # yield msg.content  # 注释掉这行，避免重复
                    pass
                else:
                    print(msg)
        except Exception as e:
            yield f"Error during agent execution: {str(e)}"

    async def answer_question_with_rag(
        self, question: str
    ) -> AsyncGenerator[str, None]:
        """
        使用RAG系统回答问题（流式响应）
        """
        async for chunk in ollama_rag_service.answer_question(question):
            yield chunk

    async def answer_question_with_sources_stream(
        self, question: str
    ) -> AsyncGenerator[Dict, None]:
        """
        使用RAG系统回答问题并流式返回答案和来源信息
        """
        async for response in ollama_rag_service.answer_question_with_sources_stream(question):
            yield response

    async def answer_question_with_sources(
        self, question: str
    ) -> Tuple[str, List[Dict]]:
        """
        使用RAG系统回答问题并返回资料来源
        """
        return await ollama_rag_service.answer_question_with_sources(question)

    async def search_documents(
        self, query: str, top_k: int = 5
    ) -> list:
        """
        搜索相关文档
        """
        return await ollama_rag_service.search_documents(query, top_k)


llm_service = LLMService()
