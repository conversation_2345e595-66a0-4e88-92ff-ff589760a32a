from ninja_extra import api_controller, route
from ninja import Schema
from .services import llm_service
from django.http import StreamingHttpResponse
import json

class ImageBase64Payload(Schema):
    image_b64: str

class QuestionPayload(Schema):
    question: str

class SearchPayload(Schema):
    query: str
    top_k: int = 5

@api_controller("/llm")
class LLMController:

    @route.post("/fire-analysis")
    async def fire_analysis(self, request, payload: ImageBase64Payload):
        """
        Receives a base64 encoded image and sends it to Ollama for fire analysis.
        Streams the response back to the client.
        """
        streaming_content = llm_service.analyze_fire_situation_with_ollama(payload.image_b64)
        return StreamingHttpResponse(streaming_content, content_type="text/event-stream")

    @route.post("/rag-qa")
    async def rag_qa(self, request, payload: QuestionPayload):
        """
        使用RAG系统回答问题（流式响应）
        """
        streaming_content = llm_service.answer_question_with_rag(payload.question)
        return StreamingHttpResponse(streaming_content, content_type="text/event-stream")

    @route.post("/rag-qa-with-sources-stream")
    async def rag_qa_with_sources_stream(self, request, payload: QuestionPayload):
        """
        使用RAG系统回答问题并流式返回答案和来源信息
        """
        async def generate_stream():
            async for response in llm_service.answer_question_with_sources_stream(payload.question):
                yield f"data: {json.dumps(response, ensure_ascii=False)}\n\n"
        
        return StreamingHttpResponse(generate_stream(), content_type="text/event-stream")

    @route.post("/rag-qa-with-sources")
    async def rag_qa_with_sources(self, request, payload: QuestionPayload):
        """
        使用RAG系统回答问题并返回资料来源
        """
        answer, sources = await llm_service.answer_question_with_sources(payload.question)
        return {
            "answer": answer,
            "sources": sources,
            "question": payload.question
        }

    @route.post("/search-documents")
    async def search_documents(self, request, payload: SearchPayload):
        """
        搜索相关文档
        """
        results = await llm_service.search_documents(payload.query, payload.top_k)
        return {"results": results}
