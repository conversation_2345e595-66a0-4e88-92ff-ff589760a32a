import pickle
import faiss
import numpy as np
import asyncio
from typing import List, Tuple, Optional, Dict
import os


class RAGService:
    def __init__(self, index_dir: str = "rag_qa"):
        """
        初始化RAG服务
        
        Args:
            index_dir: 包含向量索引文件的目录路径
        """
        self.index_dir = index_dir
        self.index = None
        self.documents = None
        self.embedder = None
        self._load_index()
        # 嵌入器将在需要时异步加载
    
    def _load_index(self):
        """加载FAISS索引和文档数据"""
        try:
            # 加载FAISS索引
            index_path = os.path.join(self.index_dir, "book_index.faiss")
            pkl_path = os.path.join(self.index_dir, "book_index.pkl")

            if os.path.exists(index_path) and os.path.exists(pkl_path):
                self.index = faiss.read_index(index_path)

                # 加载文档数据
                with open(pkl_path, 'rb') as f:
                    self.documents = pickle.load(f)

                print(f"成功加载索引，包含 {len(self.documents)} 个文档")
            else:
                print("索引文件不存在，初始化空索引")
                self.index = None
                self.documents = []
        except Exception as e:
            print(f"加载索引失败: {e}")
            # 不抛出异常，而是初始化空索引
            self.index = None
            self.documents = []
    
    async def _load_embedder(self):
        """加载嵌入模型"""
        if self.embedder is None:
            try:
                from .ollama_embedder import ollama_embedder
                self.embedder = ollama_embedder
                print("成功加载Ollama嵌入模型")
            except Exception as e:
                print(f"加载嵌入模型失败: {e}")
                raise
        return self.embedder
    
    async def search(self, query: str, top_k: int = 5) -> List[Tuple[str, float, Dict]]:
        """
        搜索相关文档
        
        Args:
            query: 查询文本
            top_k: 返回的文档数量
            
        Returns:
            相关文档列表，每个元素为(文档内容, 相似度分数, 文档元信息)
        """
        if not self.index or not self.documents:
            raise ValueError("索引未正确加载")
        
        # 确保嵌入器已加载
        embedder = await self._load_embedder()
        
        # 编码查询
        query_embedding = await embedder.encode_single(query)
        query_embedding = query_embedding.reshape(1, -1)
        
        # 搜索相似文档
        scores, indices = self.index.search(query_embedding, top_k)
        
        # 返回结果
        results = []
        for i, (score, idx) in enumerate(zip(scores[0], indices[0])):
            if idx < len(self.documents):
                doc = self.documents[idx]
                # 处理文档格式：如果是字典，提取text字段
                if isinstance(doc, dict):
                    doc_text = doc.get('text', str(doc))
                    # 提取材料名称和来源信息
                    source_info = {
                        'source': doc.get('source', '未知来源'),
                        'chunk_index': doc.get('chunk_index', 0)
                    }
                else:
                    doc_text = str(doc)
                    source_info = {
                        'source': '未知来源',
                        'chunk_index': 0
                    }
                results.append((doc_text, float(score), source_info))
        
        return results
    
    async def get_context_for_question(self, question: str, max_context_length: int = 2000) -> str:
        """
        为问题获取相关上下文
        
        Args:
            question: 用户问题
            max_context_length: 最大上下文长度
            
        Returns:
            相关上下文文本
        """
        relevant_docs = await self.search(question, top_k=3)
        
        context_parts = []
        current_length = 0
        
        for doc, score, source_info in relevant_docs:
            if current_length + len(doc) <= max_context_length:
                context_parts.append(doc)
                current_length += len(doc)
            else:
                break
        
        return "\n\n".join(context_parts)


# 创建全局RAG服务实例
rag_service = RAGService() 