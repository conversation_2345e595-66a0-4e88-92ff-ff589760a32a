#!/usr/bin/env python3
"""
测试Ollama嵌入器
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from rag_qa.ollama_embedder import ollama_embedder


async def test_ollama_embedder():
    """测试Ollama嵌入器功能"""
    print("开始测试Ollama嵌入器...")
    
    # 测试文本
    test_texts = [
        "什么是森林火灾？",
        "如何预防森林火灾？",
        "森林火灾的危害有哪些？"
    ]
    
    print("测试单个文本嵌入:")
    for i, text in enumerate(test_texts, 1):
        print(f"文本 {i}: {text}")
        try:
            embedding = await ollama_embedder.encode_single(text)
            print(f"  嵌入维度: {len(embedding)}")
            print(f"  嵌入前5个值: {embedding[:5]}")
            print()
        except Exception as e:
            print(f"  错误: {e}")
            print()
    
    print("测试批量文本嵌入:")
    try:
        embeddings = await ollama_embedder.encode(test_texts)
        print(f"批量嵌入形状: {embeddings.shape}")
        print(f"第一个嵌入前5个值: {embeddings[0][:5]}")
        print()
    except Exception as e:
        print(f"批量嵌入错误: {e}")
        print()


if __name__ == "__main__":
    asyncio.run(test_ollama_embedder()) 