import aiohttp
import json
import numpy as np
from typing import List, Optional
import asyncio


class OllamaEmbedder:
    def __init__(self, ollama_url: str = "http://localhost:11434", model_name: str = "quentinz/bge-large-zh-v1.5:f16"):
        """
        初始化Ollama嵌入器
        
        Args:
            ollama_url: Ollama服务地址
            model_name: 用于嵌入的模型名称
        """
        self.ollama_url = ollama_url
        self.model_name = model_name
        self.embedding_dim = 1024  # BGE-large-zh-v1.5的嵌入维度
    
    async def _call_ollama_embed(self, text: str) -> Optional[List[float]]:
        """
        调用Ollama进行文本嵌入
        
        Args:
            text: 要嵌入的文本
            
        Returns:
            嵌入向量
        """
        url = f"{self.ollama_url}/api/embeddings"
        
        payload = {
            "model": self.model_name,
            "prompt": text
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=payload) as response:
                    if response.status == 200:
                        data = await response.json()
                        embedding = data.get("embedding", [])
                        return embedding
                    else:
                        error_text = await response.text()
                        print(f"Ollama嵌入API错误: {response.status} - {error_text}")
                        return None
        except Exception as e:
            print(f"调用Ollama嵌入服务时发生错误: {e}")
            return None
    
    async def encode(self, texts: List[str]) -> np.ndarray:
        """
        编码文本列表为嵌入向量
        
        Args:
            texts: 文本列表
            
        Returns:
            嵌入向量数组，形状为 (len(texts), embedding_dim)
        """
        embeddings = []
        
        for text in texts:
            embedding = await self._call_ollama_embed(text)
            if embedding is not None:
                embeddings.append(embedding)
            else:
                # 如果嵌入失败，使用零向量
                embeddings.append([0.0] * self.embedding_dim)
        
        return np.array(embeddings)
    
    async def encode_single(self, text: str) -> np.ndarray:
        """
        编码单个文本为嵌入向量
        
        Args:
            text: 要嵌入的文本
            
        Returns:
            嵌入向量
        """
        result = await self.encode([text])
        return result[0] if len(result) > 0 else np.zeros(self.embedding_dim)


# 创建全局嵌入器实例
ollama_embedder = OllamaEmbedder() 