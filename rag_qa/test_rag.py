#!/usr/bin/env python3
"""
RAG服务测试脚本
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from rag_qa.ollama_rag_service import ollama_rag_service


async def test_rag_service():
    """测试RAG服务功能"""
    print("开始测试RAG服务...")
    
    # 测试问题
    test_questions = [
        "什么是森林火灾？",
        "如何预防森林火灾？",
        "森林火灾的危害有哪些？"
    ]
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n测试问题 {i}: {question}")
        print("-" * 50)
        
        try:
            async for chunk in ollama_rag_service.answer_question(question):
                print(chunk, end='', flush=True)
            print("\n")
        except Exception as e:
            print(f"错误: {e}")
    
    # 测试文档搜索
    print("\n测试文档搜索...")
    try:
        results = await ollama_rag_service.search_documents("森林火灾", top_k=3)
        print(f"搜索结果: {len(results)} 个文档")
        for i, result in enumerate(results, 1):
            print(f"文档 {i}:")
            print(f"  内容: {result['content'][:100]}...")
            print(f"  相似度: {result['score']:.4f}")
            print()
    except Exception as e:
        print(f"搜索测试错误: {e}")


if __name__ == "__main__":
    asyncio.run(test_rag_service()) 