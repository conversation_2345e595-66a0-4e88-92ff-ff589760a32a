import asyncio
import json
import aiohttp
from typing import AsyncGenerator, Dict, List, Tuple
from .rag_service import rag_service


class OllamaRAGService:
    def __init__(self, ollama_url: str = "http://localhost:11434"):
        """
        初始化Ollama RAG服务
        
        Args:
            ollama_url: Ollama服务地址
        """
        self.ollama_url = ollama_url
        self.embedding_model = "quentinz/bge-large-zh-v1.5:f16"  # 用于嵌入
        self.generation_model = "qwen3:32b-fp16"  # 用于文本生成
    
    async def _call_ollama(self, prompt: str) -> AsyncGenerator[str, None]:
        """
        调用Ollama模型进行推理
        
        Args:
            prompt: 输入提示
            
        Yields:
            模型响应的文本片段
        """
        url = f"{self.ollama_url}/api/generate"
        
        payload = {
            "model": self.generation_model,
            "prompt": prompt,
            "stream": True,
            "options": {
                "temperature": 0.7,
                "top_p": 0.9,
                "max_tokens": 2048
            }
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=payload) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        yield f"错误: Ollama服务返回状态码 {response.status}: {error_text}"
                        return
                    
                    async for line in response.content:
                        if line:
                            try:
                                data = json.loads(line.decode('utf-8'))
                                if 'response' in data:
                                    yield data['response']
                                if data.get('done', False):
                                    break
                            except json.JSONDecodeError:
                                continue
        except Exception as e:
            yield f"连接Ollama服务时发生错误: {str(e)}"
    
    async def answer_question_with_sources_stream(self, question: str, top_k: int = 5) -> AsyncGenerator[Dict, None]:
        """
        基于RAG系统回答问题并流式返回答案和来源信息
        
        Args:
            question: 用户问题
            top_k: 返回的相关文档数量
            
        Yields:
            包含答案片段和来源信息的字典
        """
        try:
            # 获取相关文档和上下文
            relevant_docs = await rag_service.search(question, top_k=top_k)
            context_parts = []
            
            # 构建上下文和来源信息
            sources = []
            for i, (doc, score, source_info) in enumerate(relevant_docs):
                context_parts.append(doc)
                sources.append({
                    "rank": i + 1,
                    "content": doc[:200] + "..." if len(doc) > 200 else doc,
                    "similarity_score": float(score),
                    "full_content": doc,
                    "source": source_info['source'],
                    "chunk_index": source_info['chunk_index']
                })
            
            context = "\n\n".join(context_parts)
            
            # 构建提示
            prompt = f"""基于以下上下文信息回答问题。如果上下文中没有相关信息，请说明无法从提供的信息中找到答案。

上下文信息：
{context}

用户问题：{question} 

请用中文回答："""
            
            async for chunk in self._call_ollama(prompt):
                yield {
                    "type": "answer_chunk",
                    "chunk": chunk
                }

            for source in sources:
                yield {
                    "type": "source",
                    "source": source
                }
        except Exception as e:
            yield {
                "type": "error",
                "error": f"处理问题时发生错误: {str(e)}"
            }
    
    async def answer_question_with_sources(self, question: str) -> Tuple[str, List[Dict]]:
        """
        基于RAG系统回答问题并返回资料来源
        
        Args:
            question: 用户问题
            
        Returns:
            (答案文本, 资料来源列表)
        """
        try:
            # 获取相关文档和上下文
            relevant_docs = await rag_service.search(question, top_k=5)
            context_parts = []
            
            # 构建上下文和来源信息
            sources = []
            for i, (doc, score, source_info) in enumerate(relevant_docs):
                context_parts.append(doc)
                sources.append({
                    "rank": i + 1,
                    "content": doc[:200] + "..." if len(doc) > 200 else doc,
                    "similarity_score": float(score),
                    "full_content": doc,
                    "source": source_info['source'],
                    "chunk_index": source_info['chunk_index']
                })
            
            context = "\n\n".join(context_parts)
            
            # 构建提示
            prompt = f"""基于以下上下文信息回答问题。如果上下文中没有相关信息，请说明无法从提供的信息中找到答案。

上下文信息：
{context}

用户问题：{question}

请用中文回答，并在回答的最后列出所参考的资料来源："""
            
            # 调用Ollama模型生成答案
            answer = ""
            async for chunk in self._call_ollama(prompt):
                answer += chunk
            
            return answer, sources
                
        except Exception as e:
            return f"处理问题时发生错误: {str(e)}", []
    
    async def answer_question(self, question: str) -> AsyncGenerator[str, None]:
        """
        基于RAG系统回答问题（流式响应）
        
        Args:
            question: 用户问题
            
        Yields:
            答案的文本片段
        """
        try:
            # 获取相关上下文
            context = await rag_service.get_context_for_question(question)
            
            # 构建提示
            prompt = f"""基于以下上下文信息回答问题。如果上下文中没有相关信息，请说明无法从提供的信息中找到答案。

上下文信息：
{context}

用户问题：{question}

请用中文回答："""
            
            # 调用Ollama模型
            async for chunk in self._call_ollama(prompt):
                yield chunk
                
        except Exception as e:
            yield f"处理问题时发生错误: {str(e)}"
    
    async def search_documents(self, query: str, top_k: int = 5) -> list:
        """
        搜索相关文档
        
        Args:
            query: 搜索查询
            top_k: 返回的文档数量
            
        Returns:
            相关文档列表
        """
        try:
            results = await rag_service.search(query, top_k)
            return [
                {
                    "content": doc,
                    "score": score,
                    "rank": i + 1,
                    "source": source_info['source'],
                    "chunk_index": source_info['chunk_index']
                }
                for i, (doc, score, source_info) in enumerate(results)
            ]
        except Exception as e:
            return [{"error": f"搜索文档时发生错误: {str(e)}"}]


# 创建全局Ollama RAG服务实例
ollama_rag_service = OllamaRAGService() 